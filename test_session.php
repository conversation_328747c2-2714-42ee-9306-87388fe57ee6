<?php
/**
 * 測試會話驗證功能
 */
require_once 'php/UserManager.php';
require_once 'php/Response.php';

$userManager = new UserManager();

echo "=== 測試會話驗證功能 ===\n\n";

// 先註冊一個測試用戶
echo "1. 註冊測試用戶...\n";
$registerResult = $userManager->register('sessiontest', '<EMAIL>', 'password123', 'Session Test User');
echo "註冊結果: " . json_encode($registerResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// 測試用戶登入
echo "2. 測試用戶登入...\n";
$loginResult = $userManager->login('sessiontest', 'password123');
echo "登入結果: " . json_encode($loginResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

if ($loginResult['success']) {
    $sessionToken = $loginResult['session_token'];
    
    // 測試會話驗證
    echo "3. 測試會話驗證...\n";
    $validateResult = $userManager->validateSession($sessionToken);
    echo "驗證結果: " . json_encode($validateResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // 測試獲取會話信息
    echo "4. 測試獲取會話信息...\n";
    $sessionInfo = $userManager->getSessionInfo($sessionToken);
    echo "會話信息: " . json_encode($sessionInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // 模擬設置 Cookie 並測試
    echo "5. 模擬 Cookie 測試...\n";
    $_COOKIE['session_token'] = $sessionToken;
    
    // 模擬 auth.php 的 validate_session 邏輯
    $sessionToken = $_COOKIE['session_token'] ?? '';
    
    if (empty($sessionToken)) {
        echo "錯誤: 未找到會話令牌\n";
    } else {
        $result = $userManager->validateSession($sessionToken);
        
        if ($result['valid']) {
            echo "成功: 會話有效\n";
            echo "用戶信息: " . json_encode($result['user'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "錯誤: 會話無效或已過期\n";
        }
    }
} else {
    echo "登入失敗，無法繼續測試\n";
}

echo "\n=== 測試完成 ===\n";
?>