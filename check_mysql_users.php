<?php
require_once 'php/config.php';
require_once 'php/DatabaseMySQLi.php';

echo "Checking MySQL users table..." . PHP_EOL;

try {
    $db = new DatabaseMySQLi();
    
    // Check if users table exists
    $tables = $db->fetchAll("SHOW TABLES LIKE 'users'");
    if (empty($tables)) {
        echo "Users table does not exist in MySQL database" . PHP_EOL;
    } else {
        echo "Users table exists" . PHP_EOL;
        
        // Check users count
        $result = $db->fetchAll("SELECT COUNT(*) as count FROM users");
        $count = $result[0]['count'];
        echo "Users count: " . $count . PHP_EOL;
        
        if ($count > 0) {
            // Show all users
            $users = $db->fetchAll("SELECT id, username, email, role, status FROM users");
            echo "Users:" . PHP_EOL;
            foreach ($users as $user) {
                echo "  - ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}, Role: {$user['role']}, Status: {$user['status']}" . PHP_EOL;
            }
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
?>
