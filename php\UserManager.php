<?php
require_once 'config.php';
require_once 'DatabaseMySQLi.php';
require_once 'SystemSettings.php';

/**
 * 用戶管理類
 * 處理用戶認證、註冊、登入、登出等功能
 */
class UserManager {
    private $db;
    private $settings;
    private $sessionTimeout = 3600; // 1小時
    private $maxFailedAttempts = 5;
    private $lockoutDuration = 900; // 15分鐘

    public function __construct() {
        try {
            $this->db = new DatabaseMySQLi();
        } catch (Exception $e) {
            // MySQL 連接失敗，回退到文件數據庫
            require_once 'FileDatabase.php';
            $this->db = new FileDatabase();
        }
        
        try {
            $this->settings = new SystemSettings();
        } catch (Exception $e) {
            // SystemSettings 初始化失敗，使用默認設置
            $this->settings = null;
        }
    }
    
    /**
     * 用戶註冊
     */
    public function register($username, $email, $password, $fullName = '') {
        try {
            // 檢查註冊是否開啟
            if ($this->settings && !$this->settings->isRegistrationEnabled()) {
                return ['success' => false, 'message' => 'Registration is currently disabled'];
            }

            // 檢查用戶名和郵箱是否已存在
            if ($this->userExists($username, $email)) {
                return ['success' => false, 'message' => 'Username or email already exists'];
            }

            // 密碼強度驗證
            if (!$this->validatePassword($password)) {
                return ['success' => false, 'message' => 'Password must be at least 8 characters long and contain letters and numbers'];
            }

            // 確定用戶狀態
            $status = ($this->settings && $this->settings->requireAdminApproval()) ? 'pending' : 'approved';
            $role = $this->settings ? $this->settings->getDefaultUserRole() : 'member';

            $userId = $this->createUser($username, $email, $password, $fullName, $role, $status);

            if ($userId) {
                $message = $status === 'pending'
                    ? 'Registration successful! Your account is pending admin approval.'
                    : 'Registration successful!';

                return [
                    'success' => true,
                    'message' => $message,
                    'user_id' => $userId,
                    'status' => $status
                ];
            }

            return ['success' => false, 'message' => 'Registration failed'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'System error: ' . $e->getMessage()];
        }
    }
    
    /**
     * 用戶登入
     */
    public function login($username, $password) {
        try {
            $user = $this->verifyPassword($username, $password);
            if (!$user) {
                return ['success' => false, 'message' => 'Invalid username or password'];
            }

            // 檢查用戶狀態
            if ($user['status'] === 'pending') {
                return ['success' => false, 'message' => 'Your account is pending admin approval'];
            }

            if ($user['status'] === 'rejected') {
                return ['success' => false, 'message' => 'Your account has been rejected'];
            }

            if ($user['status'] === 'suspended') {
                return ['success' => false, 'message' => 'Your account has been suspended'];
            }

            // 創建會話
            $sessionToken = $this->createSession($user['id']);

            return [
                'success' => true,
                'message' => 'Login successful',
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'status' => $user['status']
                ],
                'session_token' => $sessionToken
            ];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'System error: ' . $e->getMessage()];
        }
    }
    
    /**
     * 用戶登出
     */
    public function logout($sessionToken) {
        try {
            $result = $this->db->deleteSession($sessionToken);
            
            if ($result) {
                return ['success' => true, 'message' => '登出成功'];
            }
            
            return ['success' => false, 'message' => '登出失敗'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => '系統錯誤：' . $e->getMessage()];
        }
    }
    
    /**
     * 驗證會話
     */
    public function validateSession($sessionToken) {
        try {
            // 檢查數據庫類型
            if ($this->db instanceof FileDatabase) {
                // 使用文件數據庫的 validateSession 方法
                return $this->db->validateSession($sessionToken);
            } else {
                // 使用 MySQL 數據庫
                $sql = "SELECT user_id, expires_at FROM user_sessions WHERE session_token = ? AND is_active = TRUE";
                $sessions = $this->db->fetchAll($sql, [$sessionToken]);

                if (empty($sessions)) {
                    return ['valid' => false];
                }

                $session = $sessions[0];

                // 檢查是否過期
                if (strtotime($session['expires_at']) < time()) {
                    // 刪除過期會話
                    $this->db->execute("UPDATE user_sessions SET is_active = FALSE WHERE session_token = ?", [$sessionToken]);
                    return ['valid' => false];
                }

                // 延長會話時間
                $newExpiresAt = date('Y-m-d H:i:s', time() + $this->sessionTimeout);
                $this->db->execute("UPDATE user_sessions SET expires_at = ? WHERE session_token = ?", [$newExpiresAt, $sessionToken]);

                // 獲取用戶信息
                $user = $this->getUserById($session['user_id']);
                if (!$user) {
                    return ['valid' => false];
                }

                return [
                    'valid' => true,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'role' => $user['role']
                    ]
                ];
            }
        } catch (Exception $e) {
            return ['valid' => false];
        }
    }

    /**
     * 獲取會話信息
     */
    public function getSessionInfo($sessionToken) {
        try {
            // 檢查數據庫類型
            if ($this->db instanceof FileDatabase) {
                // 使用文件數據庫
                $sessionsFile = __DIR__ . '/../data/sessions.json';
                if (!file_exists($sessionsFile)) {
                    return null;
                }
                
                $content = file_get_contents($sessionsFile);
                $sessions = json_decode($content, true) ?: [];
                
                if (!isset($sessions[$sessionToken])) {
                    return null;
                }
                
                $session = $sessions[$sessionToken];
                
                // 檢查是否過期
                if (strtotime($session['expires_at']) <= time()) {
                    return null;
                }
                
                return $session;
            } else {
                // 使用 MySQL 數據庫
                $sql = "SELECT user_id, expires_at FROM user_sessions WHERE session_token = ? AND is_active = TRUE";
                $sessions = $this->db->fetchAll($sql, [$sessionToken]);

                if (empty($sessions)) {
                    return null;
                }

                $session = $sessions[0];

                // 檢查是否過期
                if (strtotime($session['expires_at']) < time()) {
                    return null;
                }

                return $session;
            }
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * 修改密碼
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            $user = $this->getUserById($userId);
            if (!$user) {
                return ['success' => false, 'message' => 'User does not exist'];
            }
            
            if (!password_verify($currentPassword, $user['password_hash'])) {
                return ['success' => false, 'message' => 'Current password is incorrect'];
            }
            
            if (!$this->validatePassword($newPassword)) {
                return ['success' => false, 'message' => 'New password must be at least 8 characters long and contain letters and numbers'];
            }
            
            $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
            
            $sql = "UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?";
            $result = $this->db->execute($sql, [$newPasswordHash, $userId]);
            
            if ($result) {
                $this->logActivity($userId, 'change_password', '用戶修改密碼');
                return ['success' => true, 'message' => 'Password changed successfully'];
            }
            
            return ['success' => false, 'message' => 'Password change failed'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => '系統錯誤：' . $e->getMessage()];
        }
    }
    
    /**
     * 更新用戶資料
     */
    public function updateProfile($userId, $email, $fullName) {
        try {
            // 檢查郵箱是否被其他用戶使用
            $sql = "SELECT id FROM users WHERE email = ? AND id != ?";
            $result = $this->db->fetchAll($sql, [$email, $userId]);
            if (!empty($result)) {
                return ['success' => false, 'message' => 'Email is already used by another user'];
            }
            
            $sql = "UPDATE users SET email = ?, full_name = ?, updated_at = NOW() WHERE id = ?";
            $result = $this->db->execute($sql, [$email, $fullName, $userId]);
            
            if ($result) {
                $this->logActivity($userId, 'update_profile', '用戶更新個人資料');
                return ['success' => true, 'message' => 'Profile updated successfully'];
            }
            
            return ['success' => false, 'message' => 'Profile update failed'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => '系統錯誤：' . $e->getMessage()];
        }
    }
    
    // 私有方法
    private function userExists($username, $email) {
        $sql = "SELECT id FROM users WHERE username = ? OR email = ?";
        $result = $this->db->fetchAll($sql, [$username, $email]);
        return !empty($result);
    }

    private function createUser($username, $email, $password, $fullName = '', $role = 'member', $status = 'pending') {
        // 檢查數據庫類型
        if ($this->db instanceof FileDatabase) {
            // 使用文件數據庫的 createUser 方法
            return $this->db->createUser($username, $email, $password, $fullName, $role, $status);
        } else {
            // 使用 MySQL 數據庫
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (username, email, role, status, password_hash, full_name, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
            $this->db->execute($sql, [$username, $email, $role, $status, $passwordHash, $fullName]);
            return $this->db->lastInsertId();
        }
    }

    private function verifyPassword($username, $password) {
        // 檢查數據庫類型
        if ($this->db instanceof FileDatabase) {
            // 使用文件數據庫的 verifyPassword 方法
            return $this->db->verifyPassword($username, $password);
        } else {
            // 使用 MySQL 數據庫
            $sql = "SELECT * FROM users WHERE username = ? AND is_active = TRUE";
            $users = $this->db->fetchAll($sql, [$username]);

            if (!empty($users)) {
                $user = $users[0];
                if (password_verify($password, $user['password_hash'])) {
                    // 更新最後登入時間
                    $updateSql = "UPDATE users SET last_login = NOW() WHERE id = ?";
                    $this->db->execute($updateSql, [$user['id']]);
                    return $user;
                }
            }
            return false;
        }
    }
    
    private function validatePassword($password) {
        return strlen($password) >= 8 && preg_match('/[A-Za-z]/', $password) && preg_match('/[0-9]/', $password);
    }
    
    private function getUserByUsername($username) {
        $sql = "SELECT * FROM users WHERE username = ? AND is_active = TRUE";
        $users = $this->db->fetchAll($sql, [$username]);
        return !empty($users) ? $users[0] : null;
    }

    public function getUserById($userId) {
        try {
            // 檢查數據庫類型
            if ($this->db instanceof FileDatabase) {
                // 使用文件數據庫
                return $this->db->findUserById($userId);
            } else {
                // 使用 MySQL 數據庫
                $sql = "SELECT * FROM users WHERE id = ? AND is_active = TRUE";
                $users = $this->db->fetchAll($sql, [$userId]);
                return !empty($users) ? $users[0] : null;
            }
        } catch (Exception $e) {
            return null;
        }
    }
    
    private function createSession($userId) {
        // 檢查數據庫類型
        if ($this->db instanceof FileDatabase) {
            // 使用文件數據庫的 createSession 方法
            return $this->db->createSession($userId);
        } else {
            // 使用 MySQL 數據庫
            $sessionToken = bin2hex(random_bytes(32));
            $expiresAt = date('Y-m-d H:i:s', time() + $this->sessionTimeout);

            $sql = "INSERT INTO user_sessions (user_id, session_token, expires_at, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)";
            $this->db->execute($sql, [
                $userId,
                $sessionToken,
                $expiresAt,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);

            return $sessionToken;
        }
    }
    
    private function isUserLocked($username) {
        // 簡化版本，FileDatabase不支持用戶鎖定功能
        return false;
    }
    
    private function recordFailedLogin($username) {
        $sql = "UPDATE users SET failed_login_attempts = failed_login_attempts + 1 WHERE username = ?";
        $this->db->execute($sql, [$username]);

        // 檢查是否需要鎖定帳戶
        $sql = "SELECT failed_login_attempts FROM users WHERE username = ?";
        $users = $this->db->fetchAll($sql, [$username]);

        if (!empty($users) && $users[0]['failed_login_attempts'] >= $this->maxFailedAttempts) {
            $lockedUntil = date('Y-m-d H:i:s', time() + $this->lockoutDuration);
            $sql = "UPDATE users SET locked_until = ? WHERE username = ?";
            $this->db->execute($sql, [$lockedUntil, $username]);
        }
    }
    
    private function resetFailedAttempts($userId) {
        $sql = "UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE id = ?";
        $this->db->execute($sql, [$userId]);
    }

    private function updateLastLogin($userId) {
        $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
        $this->db->execute($sql, [$userId]);
    }
    
    private function logActivity($userId, $action, $description) {
        $sql = "INSERT INTO user_activity_logs (user_id, action, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)";
        $this->db->execute($sql, [
            $userId,
            $action,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    }
}
?>