<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'UserManager.php';
require_once 'AdminManager.php';
require_once 'SystemSettings.php';
require_once 'Response.php';

try {
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true) ?? [];
    $action = $input['action'] ?? $_GET['action'] ?? '';
    
    // 實例化管理器，處理數據庫連接錯誤
    try {
        $userManager = new UserManager();
        $adminManager = new AdminManager();
        $settings = new SystemSettings();
    } catch (Exception $e) {
        Response::error('Database connection failed: ' . $e->getMessage());
        exit;
    }
    
    // 驗證管理員權限
    $sessionToken = $_COOKIE['session_token'] ?? null;
    $currentUser = null;
    $isAdmin = false;
    
    if ($sessionToken && $userManager->validateSession($sessionToken)) {
        $sessionInfo = $userManager->getSessionInfo($sessionToken);
        if ($sessionInfo) {
            $currentUser = $userManager->getUserById($sessionInfo['user_id']);
            $isAdmin = $adminManager->isAdmin($sessionInfo['user_id']);
        }
    }
    
    // 需要管理員權限的操作
    $adminActions = ['get_pending_users', 'get_all_users', 'approve_user', 'reject_user', 
                     'suspend_user', 'restore_user', 'get_system_settings', 'update_system_settings', 
                     'get_admin_logs'];
    
    if (in_array($action, $adminActions) && !$isAdmin) {
        Response::error('Admin access required', 403);
        exit;
    }
    
    switch ($action) {
        case 'get_pending_users':
            if ($method !== 'GET') {
                Response::error('Only GET requests allowed', 405);
            }
            
            $pendingUsers = $adminManager->getPendingUsers();
            Response::success(['users' => $pendingUsers]);
            break;
            
        case 'get_all_users':
            if ($method !== 'GET') {
                Response::error('Only GET requests allowed', 405);
            }
            
            $page = intval($_GET['page'] ?? 1);
            $limit = intval($_GET['limit'] ?? 20);
            $filters = [
                'status' => $_GET['status'] ?? '',
                'role' => $_GET['role'] ?? '',
                'search' => $_GET['search'] ?? ''
            ];
            
            $result = $adminManager->getAllUsers($page, $limit, $filters);
            Response::success($result);
            break;
            
        case 'approve_user':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }
            
            $userId = $input['user_id'] ?? 0;
            if (!$userId) {
                Response::error('User ID is required');
            }
            
            $result = $adminManager->approveUser($userId, $currentUser['id']);
            if ($result['success']) {
                Response::success($result);
            } else {
                Response::error($result['message']);
            }
            break;
            
        case 'reject_user':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }
            
            $userId = $input['user_id'] ?? 0;
            $reason = $input['reason'] ?? '';
            
            if (!$userId) {
                Response::error('User ID is required');
            }
            
            $result = $adminManager->rejectUser($userId, $currentUser['id'], $reason);
            if ($result['success']) {
                Response::success($result);
            } else {
                Response::error($result['message']);
            }
            break;
            
        case 'suspend_user':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }
            
            $userId = $input['user_id'] ?? 0;
            $reason = $input['reason'] ?? '';
            
            if (!$userId) {
                Response::error('User ID is required');
            }
            
            $result = $adminManager->suspendUser($userId, $currentUser['id'], $reason);
            if ($result['success']) {
                Response::success($result);
            } else {
                Response::error($result['message']);
            }
            break;
            
        case 'restore_user':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }
            
            $userId = $input['user_id'] ?? 0;
            if (!$userId) {
                Response::error('User ID is required');
            }
            
            $result = $adminManager->restoreUser($userId, $currentUser['id']);
            if ($result['success']) {
                Response::success($result);
            } else {
                Response::error($result['message']);
            }
            break;
            
        case 'get_system_settings':
            if ($method !== 'GET') {
                Response::error('Only GET requests allowed', 405);
            }
            
            $allSettings = $settings->getAllSettings();
            Response::success(['settings' => $allSettings]);
            break;
            
        case 'update_system_settings':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }
            
            $settingsToUpdate = $input['settings'] ?? [];
            $updated = [];
            
            foreach ($settingsToUpdate as $key => $value) {
                if ($settings->setSetting($key, $value)) {
                    $updated[] = $key;
                }
            }
            
            Response::success(['updated' => $updated]);
            break;
            
        case 'get_admin_logs':
            if ($method !== 'GET') {
                Response::error('Only GET requests allowed', 405);
            }
            
            $page = intval($_GET['page'] ?? 1);
            $limit = intval($_GET['limit'] ?? 50);
            
            $logs = $adminManager->getAdminLogs($page, $limit);
            Response::success(['logs' => $logs]);
            break;
            
        case 'check_registration_status':
            // 公開API，檢查註冊是否開啟
            $registrationEnabled = $settings->isRegistrationEnabled();
            Response::success(['registration_enabled' => $registrationEnabled]);
            break;
            
        default:
            Response::error('Invalid action', 400);
            break;
    }
    
} catch (Exception $e) {
    Response::error('System error: ' . $e->getMessage(), 500);
}
?>
