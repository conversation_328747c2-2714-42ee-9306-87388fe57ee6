<?php
// Test different passwords against the hash in users.json
$hash = '$2y$12$xBkM4/qYk9q7w2C2rQGfvOvAyXdiXe7M6Y6mqcTuIqJfF57uCDA7i';

$passwords = [
    'password123',
    'password',
    'testpassword',
    'admin',
    'test123',
    '123456'
];

echo "Testing passwords against hash..." . PHP_EOL;
echo "Hash: " . $hash . PHP_EOL . PHP_EOL;

foreach ($passwords as $password) {
    $result = password_verify($password, $hash);
    echo "Password: '$password' - " . ($result ? 'MATCH' : 'NO MATCH') . PHP_EOL;
}
?>
