<?php
/**
 * 臨時腳本：初始化用戶認證數據庫表
 */

require_once 'php/config.php';
require_once 'php/DatabaseMySQLi.php';

try {
    $database = new DatabaseMySQLi();
    $db = $database->getConnection();
    
    // 讀取SQL腳本
    $sql = file_get_contents('database/07_user_authentication.sql');
    
    // 分割SQL語句（按分號分割）
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
            $database->execute($statement);
        }
    }
    
    echo "User authentication database tables created successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>