<?php
require_once 'php/FileDatabase.php';
require_once 'php/UserManager.php';

// 測試用戶註冊
$userManager = new UserManager();

echo "測試用戶註冊...\n";
$result = $userManager->register('testuser', '<EMAIL>', 'password123');
echo json_encode($result, JSON_PRETTY_PRINT) . "\n\n";

if ($result['success']) {
    echo "測試用戶登入...\n";
    $loginResult = $userManager->login('testuser', 'password123');
    echo json_encode($loginResult, JSO<PERSON>_PRETTY_PRINT) . "\n\n";
    
    if ($loginResult['success']) {
        echo "測試會話驗證...\n";
        $sessionToken = $loginResult['session_token'];
        $validateResult = $userManager->validateSession($sessionToken);
        echo json_encode($validateResult, JSON_PRETTY_PRINT) . "\n";
    }
}
?>