/**
 * 收據生成和處理功能
 * KMS PC Receipt Maker
 */

// 當前收據數據
let currentReceiptData = null;
let currentLogo = null;

/**
 * 驗證收據表單
 */
function validateReceiptForm() {
    // Customer name is no longer required - allow empty

    // Check items using new ItemManager system
    const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];
    if (!receiptItems || receiptItems.length === 0) {
        if (typeof showMessage === 'function') {
            showMessage('請至少添加一個項目', 'error');
        } else if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
            UIManager.showMessage('Please add at least one item', 'error');
        }
        return false;
    }

    return true;
}

/**
 * 生成收據
 */
function generateReceipt() {
    // 驗證表單
    if (!validateReceiptForm()) {
        return;
    }

    // 收集表單數據
    const receiptData = collectReceiptData();

    // 生成收據HTML - Use new module
    const receiptHtml = window.ReceiptGenerator ?
        window.ReceiptGenerator.generateReceiptHtml(receiptData) :
        'Receipt generator not available';

    // 顯示預覽
    if (window.ReceiptGenerator) {
        window.ReceiptGenerator.displayReceiptPreview(receiptHtml);
    } else {
        displayReceiptPreview(receiptHtml);
    }

    // 保存當前收據數據
    currentReceiptData = receiptData;

    // 顯示操作按鈕
    const previewActions = document.getElementById('previewActions');
    if (previewActions) {
        previewActions.classList.remove('d-none');
    }

    // 顯示成功訊息
    showMessage(LanguageManager.getText('success_receipt_generated'), 'success');
}

/**
 * 收集收據數據
 */
function collectReceiptData() {
    // 客戶信息
    const customerData = {
        name: document.getElementById('customerName').value.trim(),
        phone: document.getElementById('customerPhone').value.trim(),
        email: document.getElementById('customerEmail').value.trim(),
        address: document.getElementById('customerAddress').value.trim()
    };

    // 付款方式 (使用默認值，因為表單中的字段已被移除)
    const paymentMethod = 'Cash';

    // 備註
    const notes = document.getElementById('notes').value.trim();

    // 收據號碼和日期
    let receiptNumber = document.getElementById('receiptNumber').value.trim();
    if (!receiptNumber) {
        receiptNumber = generateReceiptNumber();
    }

    // Date field removed - no longer needed
    let receiptDate = new Date(); // Use current date for internal processing only

    // 使用 ItemManager 的最新數據
    const items = window.ItemManager ? window.ItemManager.getReceiptItems() : [];

    // 計算總計
    const totals = window.ItemManager ? window.ItemManager.calculateTotals() : calculateReceiptTotals(items);

    return {
        customer: customerData,
        paymentMethod: paymentMethod,
        notes: notes,
        items: items,
        totals: totals,
        receiptNumber: receiptNumber,
        date: receiptDate
    };
}

/**
 * 計算收據總計
 */
function calculateReceiptTotals(items) {
    let subtotal = 0;

    // 計算小計
    items.forEach(item => {
        subtotal += item.totalPrice;
    });

    // 獲取折扣金額
    const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;

    // 計算稅費
    const taxRate = parseFloat(LanguageManager.getSystemConfig('tax_rate')) || 0;
    const taxAmount = Math.max(0, (subtotal - discountAmount) * taxRate);

    // 計算總計
    const totalAmount = subtotal - discountAmount + taxAmount;

    return {
        subtotal: subtotal,
        discount: discountAmount,
        tax: taxAmount,
        total: Math.max(0, totalAmount)
    };
}

/**
 * 生成收據編號
 */
function generateReceiptNumber() {
    // 預設固定編號
    return 'KMS-UltraVIP-0000001';
}

// Note: generateReceiptHtml function is now provided by receipt-generator.js module
// Old function removed to prevent conflicts


/**
 * 獲取分類文字
 */
function getCategoryText(category) {
    const categoryMap = {
        'CPU': 'cpu',
        'Motherboard': 'motherboard',
        'Memory': 'memory',
        'Graphics Card': 'graphics_card',
        'Storage': 'storage',
        'Power Supply': 'power_supply',
        'Case': 'case',
        'Cooler': 'cooler'
    };

    return LanguageManager.getText(categoryMap[category] || 'other');
}

/**
 * 獲取付款方式文字
 */
function getPaymentMethodText(method) {
    const methodMap = {
        'cash': 'payment_cash',
        'card': 'payment_card',
        'transfer': 'payment_transfer',
        'other': 'payment_other'
    };

    return LanguageManager.getText(methodMap[method] || 'payment_other');
}

/**
 * 顯示收據預覽
 */
function displayReceiptPreview(html) {
    const previewElement = document.getElementById('receiptPreview');
    previewElement.innerHTML = html;
    previewElement.classList.add('has-content');
}

/**
 * 保存收據
 */
async function saveReceipt() {
    if (!currentReceiptData) {
        await showAlert('請先生成收據', '提示');
        return;
    }

    // 顯示載入狀態
    const saveBtn = document.querySelector('[onclick="saveReceipt()"]');
    let originalText = '保存收據';

    if (saveBtn) {
        originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<span class="loading"></span> 保存中...';
        saveBtn.disabled = true;
    }

    try {

        // 發送保存請求
        const response = await fetch('php/save_receipt.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(currentReceiptData)
        });

        // 檢查響應是否為 JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            const text = await response.text();
            console.error('Non-JSON response:', text);
            showMessage('服務器返回了無效的響應格式，請檢查服務器配置', 'error');
            return;
        }

        const result = await response.json();

        if (result.success) {
            showMessage(LanguageManager.getText('success_receipt_saved'), 'success');

            // 更新收據編號
            if (result.data && result.data.receipt_number) {
                currentReceiptData.receiptNumber = result.data.receipt_number;

                // 重新生成預覽
                const receiptHtml = window.ReceiptGenerator ?
                    window.ReceiptGenerator.generateReceiptHtml(currentReceiptData) :
                    'Receipt generator not available';
                if (window.ReceiptGenerator) {
                    window.ReceiptGenerator.displayReceiptPreview(receiptHtml);
                } else {
                    displayReceiptPreview(receiptHtml);
                }

                // 生成並下載 PDF
                if (window.ReceiptGenerator && window.ReceiptGenerator.generatePDF) {
                    try {
                        window.ReceiptGenerator.generatePDF(currentReceiptData, result.data.receipt_number);
                        showMessage('收據已保存並生成PDF文件', 'success');
                    } catch (pdfError) {
                        console.error('PDF generation error:', pdfError);
                        showMessage('收據已保存，但PDF生成失敗', 'warning');
                    }
                }
            }
        } else {
            showMessage(result.message || '保存失敗', 'error');
        }

    } catch (error) {
        console.error('Save error:', error);
        if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
            showMessage('服務器響應格式錯誤，請檢查數據庫連接和服務器配置', 'error');
        } else {
            showMessage('保存時發生錯誤: ' + error.message, 'error');
        }
    } finally {
        // 恢復按鈕狀態
        const saveBtn = document.querySelector('[onclick="saveReceipt()"]');
        if (saveBtn) {
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }
    }
}

/**
 * 打印收據 - Deprecated: Now handled by ReceiptGenerator module
 * This function is kept for backward compatibility but delegates to the ReceiptGenerator instance
 */
function printReceipt() {
    // Directly call ReceiptGenerator instance to avoid recursion
    if (window.ReceiptGenerator && typeof window.ReceiptGenerator.printReceipt === 'function') {
        return window.ReceiptGenerator.printReceipt();
    } else {
        console.error('ReceiptGenerator module not available or printReceipt method not found');
        alert('Print function not available. Please ensure all modules are loaded.');
    }
}

/**
 * 顯示訊息
 */
function showMessage(message, type = 'info') {
    // 移除現有的訊息
    const existingAlert = document.querySelector('.alert-message');
    if (existingAlert) {
        existingAlert.remove();
    }

    // 創建新訊息
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-message alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 插入到頁面頂部
    const container = document.querySelector('.kms-main-content') || document.querySelector('.kms-container') || document.body;
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 自動移除
    setTimeout(() => {
        const alert = document.querySelector('.alert-message');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// Note: ReceiptGenerator is now provided by receipt-generator.js module
// Legacy functions kept for backward compatibility
window.legacyReceiptFunctions = {
    generateReceipt,
    saveReceipt,
    printReceipt,
    calculateReceiptTotals
};

// Note: generateReceiptHtml is now provided by receipt-generator.js module

// Logo 拖拽和調整大小功能
let isDragging = false;
let isResizing = false;
let dragStartX = 0;
let dragStartY = 0;
let resizeStartX = 0;
let resizeStartY = 0;
let startWidth = 0;
let startHeight = 0;

/**
 * 開始拖拽 Logo
 */
function startDrag(event) {
    if (isResizing) return;

    isDragging = true;
    dragStartX = event.clientX - currentLogo.x;
    dragStartY = event.clientY - currentLogo.y;

    document.addEventListener('mousemove', dragLogo);
    document.addEventListener('mouseup', stopDrag);

    event.preventDefault();
}

/**
 * 拖拽 Logo
 */
function dragLogo(event) {
    if (!isDragging) return;

    currentLogo.x = event.clientX - dragStartX;
    currentLogo.y = event.clientY - dragStartY;

    // 限制在收據範圍內
    const receiptElement = document.querySelector('.receipt');
    if (receiptElement) {
        const rect = receiptElement.getBoundingClientRect();
        currentLogo.x = Math.max(0, Math.min(currentLogo.x, rect.width - currentLogo.width));
        currentLogo.y = Math.max(0, Math.min(currentLogo.y, rect.height - currentLogo.height));
    }

    updateLogoPosition();
}

/**
 * 停止拖拽
 */
function stopDrag() {
    isDragging = false;
    document.removeEventListener('mousemove', dragLogo);
    document.removeEventListener('mouseup', stopDrag);
}

/**
 * 開始調整大小
 */
function startResize(event) {
    isResizing = true;
    resizeStartX = event.clientX;
    resizeStartY = event.clientY;
    startWidth = currentLogo.width;
    startHeight = currentLogo.height;

    document.addEventListener('mousemove', resizeLogo);
    document.addEventListener('mouseup', stopResize);

    event.preventDefault();
    event.stopPropagation();
}

/**
 * 調整 Logo 大小
 */
function resizeLogo(event) {
    if (!isResizing) return;

    const deltaX = event.clientX - resizeStartX;
    const deltaY = event.clientY - resizeStartY;

    currentLogo.width = Math.max(20, startWidth + deltaX);
    currentLogo.height = Math.max(20, startHeight + deltaY);

    updateLogoPosition();
}

/**
 * 停止調整大小
 */
function stopResize() {
    isResizing = false;
    document.removeEventListener('mousemove', resizeLogo);
    document.removeEventListener('mouseup', stopResize);
}

/**
 * 更新 Logo 位置和大小
 */
function updateLogoPosition() {
    const logoImg = document.querySelector('.draggable-logo');
    const resizeHandle = document.querySelector('.resize-handle');

    if (logoImg && currentLogo) {
        logoImg.style.left = currentLogo.x + 'px';
        logoImg.style.top = currentLogo.y + 'px';
        logoImg.style.width = currentLogo.width + 'px';
        logoImg.style.height = currentLogo.height + 'px';
    }

    if (resizeHandle && currentLogo) {
        resizeHandle.style.left = (currentLogo.x + currentLogo.width - 10) + 'px';
        resizeHandle.style.top = (currentLogo.y + currentLogo.height - 10) + 'px';
    }
}

// 導出拖拽函數供全局使用
window.startDrag = startDrag;
window.startResize = startResize;

// Note: Receipt generation functions are now provided by receipt-generator.js module
// window.generateReceipt - provided by receipt-generator.js
// window.generateReceiptHtml - provided by receipt-generator.js
window.generateReceiptNumber = generateReceiptNumber;
