<?php
/**
 * 獲取單個收據詳情
 * KMS PC Receipt Maker
 */

require_once 'ReceiptManager.php';
require_once 'Response.php';
require_once 'UserManager.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶認證
$userManager = new UserManager();
$sessionToken = $_COOKIE['session_token'] ?? null;

if (!$sessionToken || !$userManager->validateSession($sessionToken)) {
    Response::error('未授權訪問，請先登入', 401);
    exit;
}

try {
    // 獲取收據ID
    $receiptId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if ($receiptId <= 0) {
        Response::error('無效的收據ID', 400);
    }
    
    $receiptManager = new ReceiptManager();
    $receipt = $receiptManager->getReceiptById($receiptId);
    
    if (!$receipt) {
        Response::error('收據不存在', 404);
    }
    
    Response::success($receipt, '收據詳情獲取成功');
    
} catch (Exception $e) {
    error_log('Get receipt error: ' . $e->getMessage());
    Response::error('獲取收據詳情失敗: ' . $e->getMessage());
}
?>