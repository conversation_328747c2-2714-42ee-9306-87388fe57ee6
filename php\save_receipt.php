<?php
/**
 * 保存收據
 * KMS PC Receipt Maker
 */

// 設置錯誤處理
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 設置錯誤處理函數
set_error_handler(function($severity, $message, $file, $line) {
    error_log("PHP Error: $message in $file on line $line");
    if (!headers_sent()) {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'success' => false,
            'message' => '系統錯誤，請稍後再試',
            'error_code' => 500
        ]);
    }
    exit;
});

require_once 'ReceiptManager.php';
require_once 'Response.php';
require_once 'UserManager.php';

header('Content-Type: application/json; charset=utf-8');

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('只允許POST請求', 405);
}

// 檢查用戶認證
$userManager = new UserManager();
$sessionToken = $_COOKIE['session_token'] ?? null;

if (!$sessionToken || !$userManager->validateSession($sessionToken)) {
    Response::error('未授權訪問，請先登入', 401);
    exit;
}

try {
    // 獲取JSON數據
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        Response::error('無效的JSON數據');
    }
    
    // 驗證必需字段
    $requiredFields = ['customer', 'items', 'totals'];
    Response::validateRequired($data, $requiredFields);
    
    // 客戶姓名不再是必填項目，允許為空
    
    // 驗證項目
    if (empty($data['items']) || !is_array($data['items'])) {
        Response::error('收據項目不能為空');
    }
    
    // 清理輸入數據
    $cleanData = Response::sanitizeInput($data);
    
    // 驗證郵箱格式（如果提供）
    if (!empty($cleanData['customer']['email']) && !Response::validateEmail($cleanData['customer']['email'])) {
        Response::error('郵箱格式不正確');
    }
    
    // 驗證手機號格式（如果提供）
    if (!empty($cleanData['customer']['phone']) && !Response::validatePhone($cleanData['customer']['phone'])) {
        Response::error('手機號格式不正確');
    }
    
    // 準備收據數據
    $receiptData = [
        'customer_name' => $cleanData['customer']['name'],
        'customer_phone' => $cleanData['customer']['phone'] ?? null,
        'customer_email' => $cleanData['customer']['email'] ?? null,
        'customer_address' => $cleanData['customer']['address'] ?? null,
        'subtotal' => floatval($cleanData['totals']['subtotal']),
        'tax_amount' => floatval($cleanData['totals']['tax']),
        'discount_amount' => floatval($cleanData['totals']['discount'] ?? 0),
        'total_amount' => floatval($cleanData['totals']['total']),
        'payment_method' => $cleanData['paymentMethod'] ?? 'Cash',
        'notes' => $cleanData['notes'] ?? null
    ];
    
    // 準備項目數據
    $items = [];
    foreach ($cleanData['items'] as $item) {
        if (empty($item['name'])) {
            continue;
        }
        
        $items[] = [
            'name' => $item['name'],
            'description' => $item['description'] ?? null,
            'category' => $item['category'] ?? null,
            'quantity' => intval($item['quantity']),
            'unit_price' => floatval($item['unitPrice']),
            'total_price' => floatval($item['totalPrice']),
            'original_price' => floatval($item['originalPrice'] ?? $item['unitPrice']),
            'special_price' => isset($item['specialPrice']) ? floatval($item['specialPrice']) : null,
            'discount_percent' => intval($item['discountPercent'] ?? 0),
            'hide_price' => boolval($item['hidePrice'] ?? false)
        ];
    }
    
    if (empty($items)) {
        Response::error('沒有有效的收據項目');
    }
    
    // 保存收據
    $receiptManager = new ReceiptManager();
    $result = $receiptManager->saveReceipt($receiptData, $items);
    
    Response::success($result, '收據保存成功');
    
} catch (Exception $e) {
    error_log('Save receipt error: ' . $e->getMessage());
    Response::error('保存收據失敗: ' . $e->getMessage());
}
?>
