<?php
/**
 * 數據庫連接類 (使用PDO)
 * KMS PC Receipt Maker
 */

require_once 'config.php';

class DatabaseMySQLi {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    private $connection;
    private $error;

    /**
     * 構造函數 - 建立數據庫連接
     */
    public function __construct() {
        // 檢查 mysqli 擴展是否可用
        if (!class_exists('mysqli')) {
            throw new Exception('MySQLi extension is not available');
        }
        
        try {
            $this->connection = new mysqli($this->host, $this->username, $this->password, $this->db_name);
            
            if ($this->connection->connect_error) {
                throw new Exception("Connection failed: " . $this->connection->connect_error);
            }
            
            $this->connection->set_charset($this->charset);
            
        } catch (Exception $e) {
            $this->error = $e->getMessage();
            error_log("Database connection error: " . $this->error);
            throw new Exception("數據庫連接失敗");
        }
    }

    /**
     * 獲取連接實例
     */
    public function getConnection() {
        return $this->connection;
    }

    /**
     * 執行查詢
     */
    public function query($sql, $params = []) {
        try {
            if (!empty($params)) {
                $stmt = $this->connection->prepare($sql);
                if (!$stmt) {
                    throw new Exception("Prepare failed: " . $this->connection->error);
                }
                
                // 綁定參數
                if (!empty($params)) {
                    $types = str_repeat('s', count($params));
                    $stmt->bind_param($types, ...$params);
                }
                
                $stmt->execute();
                return $stmt;
            } else {
                $result = $this->connection->query($sql);
                if (!$result) {
                    throw new Exception("Query failed: " . $this->connection->error);
                }
                return $result;
            }
        } catch (Exception $e) {
            error_log("Query error: " . $e->getMessage());
            throw new Exception("查詢執行失敗: " . $e->getMessage());
        }
    }

    /**
     * 獲取單行數據
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        if ($stmt instanceof mysqli_stmt) {
            $result = $stmt->get_result();
            return $result->fetch_assoc();
        } else {
            return $stmt->fetch_assoc();
        }
    }

    /**
     * 獲取所有數據
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        if ($stmt instanceof mysqli_stmt) {
            $result = $stmt->get_result();
            return $result->fetch_all(MYSQLI_ASSOC);
        } else {
            $rows = [];
            while ($row = $stmt->fetch_assoc()) {
                $rows[] = $row;
            }
            return $rows;
        }
    }

    /**
     * 執行插入/更新/刪除操作
     */
    public function execute($sql, $params = []) {
        try {
            if (!empty($params)) {
                $stmt = $this->connection->prepare($sql);
                if (!$stmt) {
                    throw new Exception("Prepare failed: " . $this->connection->error);
                }
                
                $types = str_repeat('s', count($params));
                $stmt->bind_param($types, ...$params);
                $result = $stmt->execute();
                $stmt->close();
                return $result;
            } else {
                return $this->connection->query($sql);
            }
        } catch (Exception $e) {
            error_log("Execute error: " . $e->getMessage());
            throw new Exception("執行失敗: " . $e->getMessage());
        }
    }

    /**
     * 獲取最後插入的ID
     */
    public function lastInsertId() {
        return $this->connection->insert_id;
    }

    /**
     * 獲取影響的行數
     */
    public function rowCount($stmt) {
        return $stmt->rowCount();
    }

    /**
     * 開始事務
     */
    public function beginTransaction() {
        return $this->connection->autocommit(false);
    }

    /**
     * 提交事務
     */
    public function commit() {
        $result = $this->connection->commit();
        $this->connection->autocommit(true);
        return $result;
    }

    /**
     * 回滾事務
     */
    public function rollback() {
        $result = $this->connection->rollback();
        $this->connection->autocommit(true);
        return $result;
    }

    /**
     * 轉義字符串
     */
    public function escape($string) {
        return $this->connection->real_escape_string($string);
    }

    /**
     * 檢查連接是否有效
     */
    public function isConnected() {
        try {
            $this->connection->query('SELECT 1');
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 獲取錯誤信息
     */
    public function getError() {
        return $this->error;
    }

    /**
     * 關閉連接
     */
    public function close() {
        $this->connection = null;
    }

    /**
     * 析構函數
     */
    public function __destruct() {
        $this->close();
    }

    /**
     * 檢查表是否存在
     */
    public function tableExists($tableName) {
        try {
            $sql = "SELECT 1 FROM information_schema.tables WHERE table_schema = ? AND table_name = ? LIMIT 1";
            $stmt = $this->query($sql, [$this->db_name, $tableName]);
            return $stmt->fetch() !== false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 獲取表結構
     */
    public function getTableStructure($tableName) {
        try {
            $sql = "DESCRIBE `{$tableName}`";
            return $this->fetchAll($sql);
        } catch (Exception $e) {
            error_log("Get table structure error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 執行多條SQL語句
     */
    public function multiQuery($sql) {
        try {
            return $this->connection->exec($sql);
        } catch (Exception $e) {
            error_log("Multi query error: " . $e->getMessage());
            throw new Exception("批量執行失敗: " . $e->getMessage());
        }
    }

    /**
     * 獲取數據庫版本
     */
    public function getVersion() {
        try {
            $stmt = $this->query("SELECT VERSION() as version");
            $result = $stmt->fetch();
            return $result['version'];
        } catch (Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * 測試數據庫連接
     */
    public static function testConnection() {
        try {
            $db = new self();
            return $db->isConnected();
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 獲取數據庫大小
     */
    public function getDatabaseSize() {
        try {
            $sql = "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'size_mb' 
                    FROM information_schema.tables 
                    WHERE table_schema = ?";
            $stmt = $this->query($sql, [$this->db_name]);
            $result = $stmt->fetch();
            return $result['size_mb'] ?? 0;
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 備份數據庫
     */
    public function backup($filename = null) {
        if (!$filename) {
            $filename = $this->db_name . '_backup_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        try {
            $tables = $this->fetchAll("SHOW TABLES");
            $backup = "-- Database backup for {$this->db_name}\n";
            $backup .= "-- Generated on " . date('Y-m-d H:i:s') . "\n\n";
            
            foreach ($tables as $table) {
                $tableName = array_values($table)[0];
                $backup .= "-- Table: {$tableName}\n";
                $backup .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
                
                $createTable = $this->fetch("SHOW CREATE TABLE `{$tableName}`");
                $backup .= $createTable['Create Table'] . ";\n\n";
                
                $rows = $this->fetchAll("SELECT * FROM `{$tableName}`");
                if (!empty($rows)) {
                    $backup .= "INSERT INTO `{$tableName}` VALUES\n";
                    $values = [];
                    foreach ($rows as $row) {
                        $values[] = "('" . implode("','", array_map([$this, 'escape'], $row)) . "')";
                    }
                    $backup .= implode(",\n", $values) . ";\n\n";
                }
            }
            
            return file_put_contents($filename, $backup);
        } catch (Exception $e) {
            error_log("Backup error: " . $e->getMessage());
            return false;
        }
    }
}
?>
