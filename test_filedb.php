<?php
require_once 'php/FileDatabase.php';

echo "Testing FileDatabase authentication..." . PHP_EOL;

try {
    $db = new FileDatabase();
    
    // Test verifyPassword
    $result = $db->verifyPassword('testuser', 'password123');
    
    if ($result) {
        echo "Authentication successful!" . PHP_EOL;
        echo "User: " . json_encode($result, JSON_PRETTY_PRINT) . PHP_EOL;
    } else {
        echo "Authentication failed!" . PHP_EOL;
        
        // Let's check if the user exists
        $user = $db->findUserByUsername('testuser');
        if ($user) {
            echo "User exists: " . json_encode($user, JSON_PRETTY_PRINT) . PHP_EOL;
            
            // Test password verification manually
            $passwordCheck = password_verify('password123', $user['password']);
            echo "Password verification result: " . ($passwordCheck ? 'true' : 'false') . PHP_EOL;
        } else {
            echo "User not found!" . PHP_EOL;
        }
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
?>
