<!-- =================================================================
管理員面板模態框
================================================================== -->
<div class="modal fade" id="adminPanelModal" tabindex="-1" aria-labelledby="adminPanelModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content add-item-modal-content">
            <header class="modal-header add-item-modal-header">
                <div class="modal-title-container">
                    <div class="modal-icon">
                        <span class="icon-symbol">👑</span>
                    </div>
                    <h5 class="modal-title" id="adminPanelModalLabel">
                        <span data-lang="admin_panel">管理員面板</span>
                    </h5>
                </div>
                <button type="button" class="btn-close add-item-modal-close" data-bs-dismiss="modal" aria-label="Close">
                    <span class="icon-symbol">✕</span>
                </button>
            </header>
            <div class="modal-body add-item-modal-body">
                <ul class="nav nav-tabs" id="adminTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="pending-users-tab" data-bs-toggle="tab" data-bs-target="#pending-users" type="button" role="tab" aria-controls="pending-users" aria-selected="true">
                            <span class="icon-symbol">⏳</span>
                            <span data-lang="admin_pending_users">待審核用戶</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="all-users-tab" data-bs-toggle="tab" data-bs-target="#all-users" type="button" role="tab" aria-controls="all-users" aria-selected="false">
                            <span class="icon-symbol">👥</span>
                            <span data-lang="admin_all_users">所有用戶</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="system-settings-tab" data-bs-toggle="tab" data-bs-target="#system-settings" type="button" role="tab" aria-controls="system-settings" aria-selected="false">
                            <span class="icon-symbol">⚙️</span>
                            <span data-lang="admin_system_settings">系統設定</span>
                        </button>
                    </li>
                </ul>
                <div class="tab-content" id="adminTabContent">
                    <div class="tab-pane fade show active" id="pending-users" role="tabpanel" aria-labelledby="pending-users-tab">
                        <div class="mt-3">
                            <h6 data-lang="admin_pending_users_desc">待審核的用戶註冊申請</h6>
                            <div id="pendingUsersContainer">
                                <!-- 待審核用戶列表將在這裡動態載入 -->
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="all-users" role="tabpanel" aria-labelledby="all-users-tab">
                        <div class="mt-3">
                            <h6 data-lang="admin_all_users_desc">系統中的所有用戶</h6>
                            <div id="allUsersContainer">
                                <!-- 所有用戶列表將在這裡動態載入 -->
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="system-settings" role="tabpanel" aria-labelledby="system-settings-tab">
                        <div class="mt-3">
                            <h6 data-lang="admin_system_settings_desc">系統配置和設定</h6>
                            <div class="form-section">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="systemName" class="form-label">
                                            <span class="icon-symbol">🏢</span>
                                            <span data-lang="admin_system_name">系統名稱</span>
                                        </label>
                                        <input type="text" class="form-control modern-input" id="systemName" value="KMS PC Receipt Maker">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="systemVersion" class="form-label">
                                            <span class="icon-symbol">📦</span>
                                            <span data-lang="admin_system_version">系統版本</span>
                                        </label>
                                        <input type="text" class="form-control modern-input" id="systemVersion" value="1.0.0" readonly>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="defaultLanguage" class="form-label">
                                            <span class="icon-symbol">🌐</span>
                                            <span data-lang="admin_default_language">預設語言</span>
                                        </label>
                                        <select class="form-select modern-input" id="defaultLanguage">
                                            <option value="zh-TW">繁體中文</option>
                                            <option value="zh-CN">简体中文</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="maxUsers" class="form-label">
                                            <span class="icon-symbol">👥</span>
                                            <span data-lang="admin_max_users">最大用戶數</span>
                                        </label>
                                        <input type="number" class="form-control modern-input" id="maxUsers" value="100" min="1">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="allowRegistration" checked>
                                            <label class="form-check-label" for="allowRegistration">
                                                <span data-lang="admin_allow_registration">允許用戶註冊</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="requireApproval" checked>
                                            <label class="form-check-label" for="requireApproval">
                                                <span data-lang="admin_require_approval">新用戶需要審核</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" onclick="saveSystemSettings()">
                                    <span class="icon-symbol">💾</span>
                                    <span data-lang="admin_save_settings">保存設定</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <footer class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-lang="close">關閉</button>
            </footer>
        </div>
    </div>
</div>