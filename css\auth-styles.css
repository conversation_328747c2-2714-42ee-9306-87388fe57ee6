/* ================================================================
   認證系統樣式
   ================================================================ */

:root {
  --auth-primary: var(--color-2);
  --auth-secondary: var(--color-3);
  --auth-success: var(--color-1);
  --auth-warning: var(--color-4);
  --auth-text: var(--text-color-1);
  --auth-text-light: var(--text-color-2);
  --auth-border: var(--border-color-1);
  --auth-bg: var(--color-2);
  --auth-hover: var(--color-5);
}

/* ================================================================
   基礎樣式
   ================================================================ */

/* 認證按鈕組 */
.kms-auth-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
}

.kms-auth-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--auth-primary);
  color: var(--auth-text-light);
  border: 1px solid var(--auth-border);
  border-radius: 0.375rem;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.kms-auth-btn:hover {
  background: var(--auth-hover);
  color: var(--auth-text-light);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.kms-auth-btn:active {
  transform: translateY(0);
}

/* 認證狀態顯示控制 */
.auth-logged-out {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-logged-in {
  display: none;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--auth-success);
  color: var(--auth-text);
  border-radius: 0.375rem;
  font-weight: 500;
}

.user-menu {
  display: flex;
  gap: 0.5rem;
}

/* ================================================================
   布局樣式
   ================================================================ */

/* 模態框樣式增強 */
.modal-content {
  border: none;
  border-radius: 1rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.modal-header {
  background: linear-gradient(135deg, var(--auth-primary), var(--auth-hover));
  color: var(--auth-text-light);
  border-bottom: none;
  border-radius: 1rem 1rem 0 0;
  padding: 1.5rem;
}

.modal-title {
  font-weight: 600;
  font-size: 1.25rem;
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  border-top: 1px solid var(--border-color-3);
  padding: 1.5rem;
  background: rgba(0, 255, 234, 0.05);
}

/* 表單樣式增強 */
.form-label {
  font-weight: 600;
  color: var(--auth-text);
  margin-bottom: 0.5rem;
}

.form-control {
  border: 2px solid var(--border-color-3);
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--auth-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 136, 255, 0.25);
  outline: none;
}

.form-control:invalid {
  border-color: var(--auth-warning);
}

.form-check-input {
  border: 2px solid var(--border-color-3);
  border-radius: 0.25rem;
}

.form-check-input:checked {
  background-color: var(--auth-primary);
  border-color: var(--auth-primary);
}

/* ================================================================
   交互樣式
   ================================================================ */

/* 按鈕樣式 */
.btn {
  border-radius: 0.5rem;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: var(--auth-primary);
  border-color: var(--auth-primary);
  color: var(--auth-text-light);
}

.btn-primary:hover {
  background: var(--auth-hover);
  border-color: var(--auth-hover);
  transform: translateY(-1px);
}

.btn-success {
  background: var(--auth-success);
  border-color: var(--auth-success);
  color: var(--auth-text);
}

.btn-success:hover {
  background: var(--color-1);
  border-color: var(--color-1);
  transform: translateY(-1px);
}

.btn-warning {
  background: var(--auth-warning);
  border-color: var(--auth-warning);
  color: var(--auth-text-light);
}

.btn-warning:hover {
  background: var(--color-4);
  border-color: var(--color-4);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--border-color-1);
  border-color: var(--border-color-1);
  color: var(--auth-text-light);
}

.btn-secondary:hover {
  background: var(--border-color-2);
  border-color: var(--border-color-2);
  color: var(--auth-text);
  transform: translateY(-1px);
}

.btn-link {
  color: var(--auth-primary);
  text-decoration: none;
  font-weight: 500;
}

.btn-link:hover {
  color: var(--auth-hover);
  text-decoration: underline;
}

/* 關閉按鈕 */
.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--auth-text-light);
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.btn-close:hover {
  opacity: 1;
}

/* ================================================================
   響應式樣式
   ================================================================ */

@media (max-width: 768px) {
  .kms-auth-group {
    flex-direction: column;
    gap: 0.25rem;
    margin-left: 0.5rem;
  }
  
  .kms-auth-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .auth-logged-in {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .user-menu {
    flex-direction: column;
    width: 100%;
  }
  
  .modal-body {
    padding: 1.5rem;
  }
  
  .modal-footer {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .kms-auth-group {
    display: none;
  }
  
  .modal-dialog {
    margin: 1rem;
  }
  
  .modal-body {
    padding: 1rem;
  }
}

/* 載入動畫 */
.auth-loading {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--auth-text-light);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 錯誤提示 */
.auth-error {
  color: var(--auth-warning);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.auth-success-msg {
  color: var(--auth-success);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* 表單驗證樣式 */
.is-invalid {
  border-color: var(--auth-warning) !important;
}

.is-valid {
  border-color: var(--auth-success) !important;
}

.invalid-feedback {
  color: var(--auth-warning);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.valid-feedback {
  color: var(--auth-success);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Bootstrap Display Utilities */
.d-none {
  display: none !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}