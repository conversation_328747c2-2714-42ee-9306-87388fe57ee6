<?php

class FileDatabase {
    private $dataDir;
    private $usersFile;
    
    public function __construct() {
        $this->dataDir = __DIR__ . '/../data';
        $this->usersFile = $this->dataDir . '/users.json';
        
        // 創建數據目錄
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
        
        // 初始化用戶文件
        if (!file_exists($this->usersFile)) {
            file_put_contents($this->usersFile, json_encode([]));
        }
    }
    
    /**
     * 獲取所有用戶
     */
    private function getUsers() {
        $content = file_get_contents($this->usersFile);
        return json_decode($content, true) ?: [];
    }
    
    /**
     * 保存用戶數據
     */
    private function saveUsers($users) {
        return file_put_contents($this->usersFile, json_encode($users, JSON_PRETTY_PRINT));
    }
    
    /**
     * 根據用戶名查找用戶
     */
    public function findUserByUsername($username) {
        $users = $this->getUsers();
        foreach ($users as $user) {
            if ($user['username'] === $username) {
                return $user;
            }
        }
        return null;
    }
    
    /**
     * 根據郵箱查找用戶
     */
    public function findUserByEmail($email) {
        $users = $this->getUsers();
        foreach ($users as $user) {
            if ($user['email'] === $email) {
                return $user;
            }
        }
        return null;
    }
    
    /**
     * 創建新用戶
     */
    public function createUser($username, $email, $password, $fullName = '', $role = 'member', $status = 'pending') {
        $users = $this->getUsers();
        
        // 檢查用戶名是否已存在
        if ($this->findUserByUsername($username)) {
            return false;
        }
        
        // 檢查郵箱是否已存在
        if ($this->findUserByEmail($email)) {
            return false;
        }
        
        $newUser = [
            'id' => count($users) + 1,
            'username' => $username,
            'email' => $email,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'full_name' => $fullName,
            'role' => $role,
            'status' => $status,
            'is_active' => true,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $users[] = $newUser;
        $this->saveUsers($users);
        
        return $newUser['id'];
    }
    
    /**
     * 驗證用戶密碼
     */
    public function verifyPassword($username, $password) {
        $user = $this->findUserByUsername($username);
        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }
        return false;
    }
    
    /**
     * 更新用戶密碼
     */
    public function updatePassword($username, $newPassword) {
        $users = $this->getUsers();
        
        for ($i = 0; $i < count($users); $i++) {
            if ($users[$i]['username'] === $username) {
                $users[$i]['password'] = password_hash($newPassword, PASSWORD_DEFAULT);
                $users[$i]['updated_at'] = date('Y-m-d H:i:s');
                $this->saveUsers($users);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 更新用戶資料
     */
    public function updateUser($username, $data) {
        $users = $this->getUsers();
        
        for ($i = 0; $i < count($users); $i++) {
            if ($users[$i]['username'] === $username) {
                foreach ($data as $key => $value) {
                    if ($key !== 'id' && $key !== 'username' && $key !== 'created_at') {
                        $users[$i][$key] = $value;
                    }
                }
                $users[$i]['updated_at'] = date('Y-m-d H:i:s');
                $this->saveUsers($users);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 創建會話
     */
    public function createSession($userId) {
        $sessionToken = bin2hex(random_bytes(32));
        $sessionsFile = $this->dataDir . '/sessions.json';
        
        // 獲取現有會話
        $sessions = [];
        if (file_exists($sessionsFile)) {
            $content = file_get_contents($sessionsFile);
            $sessions = json_decode($content, true) ?: [];
        }
        
        // 清理過期會話
        $sessions = array_filter($sessions, function($session) {
            return strtotime($session['expires_at']) > time();
        });
        
        // 添加新會話
        $sessions[$sessionToken] = [
            'user_id' => $userId,
            'session_token' => $sessionToken,
            'expires_at' => date('Y-m-d H:i:s', time() + 3600), // 1小時
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        file_put_contents($sessionsFile, json_encode($sessions, JSON_PRETTY_PRINT));
        return $sessionToken;
    }
    
    /**
     * 驗證會話
     */
    public function validateSession($sessionToken) {
        $sessionsFile = $this->dataDir . '/sessions.json';
        
        if (!file_exists($sessionsFile)) {
            return ['valid' => false];
        }
        
        $content = file_get_contents($sessionsFile);
        $sessions = json_decode($content, true) ?: [];
        
        if (!isset($sessions[$sessionToken])) {
            return ['valid' => false];
        }
        
        $session = $sessions[$sessionToken];
        
        // 檢查是否過期
        if (strtotime($session['expires_at']) <= time()) {
            unset($sessions[$sessionToken]);
            file_put_contents($sessionsFile, json_encode($sessions, JSON_PRETTY_PRINT));
            return ['valid' => false];
        }
        
        // 延長會話時間
        $sessions[$sessionToken]['expires_at'] = date('Y-m-d H:i:s', time() + 3600);
        file_put_contents($sessionsFile, json_encode($sessions, JSON_PRETTY_PRINT));
        
        // 獲取用戶信息
        $user = $this->findUserById($session['user_id']);
        if (!$user) {
            return ['valid' => false];
        }
        
        return [
            'valid' => true,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'role' => $user['role'] ?? 'member'
            ]
        ];
    }
    
    /**
     * 根據ID查找用戶
     */
    public function findUserById($id) {
        $users = $this->getUsers();
        foreach ($users as $user) {
            if ($user['id'] == $id) {
                return $user;
            }
        }
        return null;
    }
    
    /**
     * 刪除會話
     */
    public function deleteSession($sessionToken) {
        $sessionsFile = $this->dataDir . '/sessions.json';
        
        if (!file_exists($sessionsFile)) {
            return true;
        }
        
        $content = file_get_contents($sessionsFile);
        $sessions = json_decode($content, true) ?: [];
        
        if (isset($sessions[$sessionToken])) {
            unset($sessions[$sessionToken]);
            file_put_contents($sessionsFile, json_encode($sessions, JSON_PRETTY_PRINT));
        }
        
        return true;
    }
    
    /**
     * 獲取系統設置
     */
    public function getSetting($key, $default = null) {
        $settingsFile = $this->dataDir . '/settings.json';
        
        if (!file_exists($settingsFile)) {
            return $default;
        }
        
        $content = file_get_contents($settingsFile);
        $settings = json_decode($content, true) ?: [];
        
        if (isset($settings[$key])) {
            $value = $settings[$key]['value'];
            // 嘗試解析布爾值
            if ($value === 'true') return true;
            if ($value === 'false') return false;
            return $value;
        }
        
        return $default;
    }
    
    /**
     * 設置系統設置
     */
    public function setSetting($key, $value, $description = null) {
        $settingsFile = $this->dataDir . '/settings.json';
        
        // 獲取現有設置
        $settings = [];
        if (file_exists($settingsFile)) {
            $content = file_get_contents($settingsFile);
            $settings = json_decode($content, true) ?: [];
        }
        
        // 轉換布爾值為字符串
        if (is_bool($value)) {
            $value = $value ? 'true' : 'false';
        }
        
        // 設置新值
        $settings[$key] = [
            'value' => $value,
            'description' => $description,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 保存設置
        return file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT)) !== false;
    }
    
    /**
     * 模擬 SQL 查詢的 fetchAll 方法
     */
    public function fetchAll($sql, $params = []) {
        $users = $this->getUsers();
        
        // 簡單的 SQL 解析和執行
        if (strpos($sql, 'SELECT role FROM users WHERE id = ?') !== false) {
            $userId = $params[0];
            foreach ($users as $user) {
                if ($user['id'] == $userId && isset($user['is_active']) && $user['is_active']) {
                    return [['role' => $user['role'] ?? 'user']];
                }
            }
            return [];
        }
        
        if (strpos($sql, 'SELECT id, username, email, full_name, created_at FROM users WHERE status = ?') !== false) {
            $status = $params[0];
            $result = [];
            foreach ($users as $user) {
                if (isset($user['status']) && $user['status'] === $status && isset($user['is_active']) && $user['is_active']) {
                    $result[] = [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'full_name' => $user['full_name'] ?? '',
                        'created_at' => $user['created_at']
                    ];
                }
            }
            return $result;
        }
        
        // 其他查詢暫時返回空數組
        return [];
    }
    
    /**
     * 模擬 SQL 執行的 execute 方法
     */
    public function execute($sql, $params = []) {
        // 對於文件數據庫，大部分執行操作都通過其他方法處理
        return true;
    }
}