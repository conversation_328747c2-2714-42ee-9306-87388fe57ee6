<?php
require_once 'php/config.php';
require_once 'php/UserManager.php';

echo "Testing UserManager login..." . PHP_EOL;

try {
    $userManager = new UserManager();
    
    // Test login
    $result = $userManager->login('testuser', 'password123');
    
    echo "Login result: " . json_encode($result, JSON_PRETTY_PRINT) . PHP_EOL;
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
    echo "Stack trace: " . $e->getTraceAsString() . PHP_EOL;
}
?>
