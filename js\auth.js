/**
 * 認證系統 JavaScript
 * 處理用戶登入、登出、註冊等功能
 */

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.init();
    }

    /**
     * 初始化認證管理器
     */
    init() {
        this.bindEvents();
        this.checkSession();
        this.checkRegistrationStatus();
        this.updateUI();
    }

    /**
     * 綁定事件監聽器
     */
    bindEvents() {
        // 登入表單
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // 註冊表單
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }

        // 忘記密碼表單
        const forgotPasswordForm = document.getElementById('forgotPasswordForm');
        if (forgotPasswordForm) {
            forgotPasswordForm.addEventListener('submit', (e) => this.handleForgotPassword(e));
        }

        // 用戶設定表單
        const userSettingsForm = document.getElementById('userSettingsForm');
        if (userSettingsForm) {
            userSettingsForm.addEventListener('submit', (e) => this.handleUserSettings(e));
        }

        // 個人資料表單
        const profileForm = document.getElementById('profileForm');
        if (profileForm) {
            profileForm.addEventListener('submit', (e) => this.handleUpdateProfile(e));
        }

        // 密碼變更表單
        const changePasswordForm = document.getElementById('changePasswordForm');
        if (changePasswordForm) {
            changePasswordForm.addEventListener('submit', (e) => this.handleChangePassword(e));
        }

        // 登出按鈕
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => this.handleLogout(e));
        }

        // 模態框重置
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('hidden.bs.modal', () => {
                this.resetModalForms(modal);
            });
        });
    }

    /**
     * 檢查用戶會話狀態
     */
    async checkSession() {
        try {
            // 不需要發送令牌，後端會檢查PHP會話
            const response = await this.makeRequest('validate_session', {}, 'GET');
            if (response.success && response.valid) {
                this.currentUser = response.user;
                this.isLoggedIn = true;
            } else {
                this.currentUser = null;
                this.isLoggedIn = false;
            }
        } catch (error) {
            this.currentUser = null;
            this.isLoggedIn = false;
            if (error.message && !error.message.includes('401')) {
                console.error('Session validation error:', error);
            }
        }
        this.updateUI();
    }

    /**
     * 處理登入
     */
    async handleLogin(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]') || document.querySelector('button[onclick="performLogin()"]');

        this.setLoading(submitBtn, true);
        this.clearErrors(form);

        try {
            const response = await this.makeRequest('login', {
                username: formData.get('username'),
                password: formData.get('password'),
                remember: formData.get('remember') === 'on'
            });

            if (response.success) {
                this.currentUser = response.user;
                this.isLoggedIn = true;

                console.log('Login successful, user:', this.currentUser);

                // 關閉模態框
                this.closeModal('loginModal');

                // 顯示成功訊息
                this.showMessage(this.getText('auth_login_success'), 'success');

                // 強制更新UI多次確保生效
                setTimeout(() => {
                    this.updateUI();
                    console.log('First UI update completed');
                }, 100);

                setTimeout(() => {
                    this.updateUI();
                    console.log('Second UI update completed');
                }, 500);

                // 檢查註冊按鈕顯示狀態
                this.checkRegistrationStatus();

                // 觸發登入成功事件，通知其他組件
                this.triggerLoginEvent();

                console.log('Login process completed');
            } else {
                this.showError(form, response.message || this.getText('auth_login_failed'));
            }
        } catch (error) {
            this.showError(form, this.getText('auth_network_error'));
        } finally {
            this.setLoading(submitBtn, false);
        }
    }

    /**
     * 處理註冊
     */
    async handleRegister(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // 驗證密碼確認
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        
        if (password !== confirmPassword) {
            this.showError(form, this.getText('auth_password_mismatch'));
            return;
        }

        this.setLoading(submitBtn, true);
        this.clearErrors(form);

        try {
            const response = await this.makeRequest('register', {
                username: formData.get('username'),
                email: formData.get('email'),
                password: password
            });

            if (response.success) {
                this.closeModal('registerModal');
                this.showMessage(this.getText('auth_register_success'), 'success');
                // 自動打開登入模態框
                setTimeout(() => {
                    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
                    loginModal.show();
                }, 1000);
            } else {
                this.showError(form, response.message || this.getText('auth_register_failed'));
            }
        } catch (error) {
            this.showError(form, this.getText('auth_network_error'));
        } finally {
            this.setLoading(submitBtn, false);
        }
    }

    /**
     * 處理忘記密碼
     */
    async handleForgotPassword(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        this.setLoading(submitBtn, true);
        this.clearErrors(form);

        try {
            const response = await this.makeRequest('forgot_password', {
                email: formData.get('email')
            });

            if (response.success) {
                this.closeModal('forgotPasswordModal');
                this.showMessage(this.getText('auth_password_reset_sent'), 'success');
            } else {
                this.showError(form, response.message || this.getText('auth_send_failed'));
            }
        } catch (error) {
            this.showError(form, this.getText('auth_network_error'));
        } finally {
            this.setLoading(submitBtn, false);
        }
    }

    /**
     * 處理個人資料更新
     */
    async handleUpdateProfile(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="button"]');
        
        this.setLoading(submitBtn, true);
        this.clearErrors(form);

        try {
            const response = await this.makeRequest('update_profile', {
                email: formData.get('email'),
                full_name: formData.get('full_name')
            });

            if (response.success) {
                // 更新當前用戶資訊
                if (this.currentUser) {
                    this.currentUser.email = formData.get('email');
                    this.currentUser.full_name = formData.get('full_name');
                }
                this.updateUI();
                this.showMessage(this.getText('auth_profile_updated') || '個人資料更新成功！', 'success');
            } else {
                this.showError(form, response.message || this.getText('auth_update_failed') || '更新失敗');
            }
        } catch (error) {
            this.showError(form, this.getText('auth_network_error') || '網路錯誤，請稍後再試');
        } finally {
            this.setLoading(submitBtn, false);
        }
    }

    /**
     * 處理密碼變更
     */
    async handleChangePassword(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="button"]');
        
        // 驗證新密碼確認
        const newPassword = formData.get('new_password');
        const confirmNewPassword = formData.get('confirm_new_password');
        
        if (newPassword !== confirmNewPassword) {
            this.showError(form, '新密碼確認不一致');
            return;
        }

        this.setLoading(submitBtn, true);
        this.clearErrors(form);

        try {
            const response = await this.makeRequest('change_password', {
                current_password: formData.get('current_password'),
                new_password: newPassword
            });

            if (response.success) {
                form.reset();
                this.showMessage('密碼變更成功！', 'success');
            } else {
                this.showError(form, response.message || '密碼變更失敗');
            }
        } catch (error) {
            this.showError(form, '網路錯誤，請稍後再試');
        } finally {
            this.setLoading(submitBtn, false);
        }
    }

    /**
     * 處理登出
     */
    async handleLogout(event) {
        if(event) event.preventDefault();
        
        try {
            // 調用後端登出，後端負責銷毀會話
            await this.makeRequest('logout', {}, 'POST');
        } catch (error) {
            console.error('Logout request failed:', error);
        } finally {
            // 無論後端如何，都清除前端狀態
            this.currentUser = null;
            this.isLoggedIn = false;
            this.updateUI();
            this.showMessage(this.getText('auth_logout_success') || 'Logged out successfully', 'success');
            
            // 如果當前在 History 頁面，重新導向到首頁
            if (window.location.hash === '#history') {
                window.location.hash = '#create';
            }
        }
    }

    /**
     * 更新UI狀態
     */
    updateUI() {

        const loggedOutElements = document.querySelectorAll('.auth-logged-out');
        const loggedInElements = document.querySelectorAll('.auth-logged-in');
        const usernameDisplay = document.getElementById('currentUsername');



        if (this.isLoggedIn && this.currentUser) {

            // 顯示已登入狀態
            loggedOutElements.forEach(el => {
                el.classList.add('d-none');
                el.classList.remove('d-flex');
            });
            loggedInElements.forEach(el => {
                el.classList.remove('d-none');
                el.classList.add('d-flex');
            });

            if (usernameDisplay) {
                usernameDisplay.textContent = this.currentUser.username;
            }

            // 顯示/隱藏管理員按鈕
            const adminBtn = document.getElementById('adminPanelBtn');
            if (adminBtn) {
                if (this.currentUser.role === 'admin') {
                    adminBtn.classList.remove('d-none');
                    adminBtn.classList.add('d-inline-block');
                } else {
                    adminBtn.classList.add('d-none');
                    adminBtn.classList.remove('d-inline-block');
                }
            }

            // 填充用戶設定表單
            this.populateUserSettingsForm();
        } else {
            // 顯示未登入狀態
            loggedOutElements.forEach(el => {
                el.classList.remove('d-none');
                el.classList.add('d-flex');
            });
            loggedInElements.forEach(el => {
                el.classList.add('d-none');
                el.classList.remove('d-flex');
            });

            // 隱藏管理員按鈕
            const adminBtn = document.getElementById('adminPanelBtn');
            if (adminBtn) {
                adminBtn.classList.add('d-none');
                adminBtn.classList.remove('d-inline-block');
            }
        }
        
        // 更新 History 按鈕狀態
        this.updateHistoryAccess();
        
        // 通知 UIManager 更新導航按鈕
        if (window.UIManager && typeof window.UIManager.updateNavigationButtons === 'function') {
            window.UIManager.updateNavigationButtons();
        }
    }

    /**
     * 更新 History 訪問權限
     */
    updateHistoryAccess() {
        const historyBtn = document.querySelector('[data-section="history"]');
        if (historyBtn) {
            if (this.isLoggedIn) {
                historyBtn.classList.remove('disabled');
                historyBtn.removeAttribute('disabled');
            } else {
                historyBtn.classList.add('disabled');
                historyBtn.setAttribute('disabled', 'true');
                
                // 如果當前在 History 頁面且未登入，跳轉到登入
                if (window.location.hash === '#history') {
                    this.showLoginRequired();
                }
            }
        }
    }

    /**
     * 顯示需要登入的提示
     */
    showLoginRequired() {
        this.showMessage('請先登入以查看歷史記錄', 'warning');
        setTimeout(() => {
            const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
            loginModal.show();
        }, 1000);
    }

    /**
     * 填充用戶設定表單
     */
    populateUserSettingsForm() {
        if (!this.currentUser) return;

        const usernameInput = document.getElementById('profileUsername');
        const emailInput = document.getElementById('profileEmail');
        const fullNameInput = document.getElementById('profileFullName');

        if (usernameInput) usernameInput.value = this.currentUser.username || '';
        if (emailInput) emailInput.value = this.currentUser.email || '';
        if (fullNameInput) fullNameInput.value = this.currentUser.full_name || '';
    }

    /**
     * 發送API請求
     */
    async makeRequest(action, data = {}) {
        const response = await fetch('php/auth.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                ...data
            })
        });

        if (!response.ok) {
            // 對於401錯誤，嘗試解析JSON響應以獲取更詳細的錯誤信息
            if (response.status === 401) {
                try {
                    const errorData = await response.json();
                    throw new Error(`HTTP error! status: ${response.status} - ${errorData.message || 'Unauthorized'}`);
                } catch (jsonError) {
                    throw new Error(`HTTP error! status: ${response.status} - Unauthorized`);
                }
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * 設置載入狀態
     */
    setLoading(button, loading) {
        if (!button) return;

        if (loading) {
            // 保存原始內容
            if (!button.getAttribute('data-original-html')) {
                button.setAttribute('data-original-html', button.innerHTML);
            }
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>' + this.getText('auth_processing');
        } else {
            button.disabled = false;
            const originalHtml = button.getAttribute('data-original-html');
            if (originalHtml) {
                button.innerHTML = originalHtml;
            }
        }
    }

    /**
     * 顯示錯誤訊息
     */
    showError(form, message) {
        let errorDiv = form.querySelector('.auth-error');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'auth-error';
            form.insertBefore(errorDiv, form.firstChild);
        }
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    }

    /**
     * 清除錯誤訊息
     */
    clearErrors(form) {
        const errorDiv = form.querySelector('.auth-error');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    /**
     * 顯示全局訊息
     */
    showMessage(message, type = 'info') {
        // 創建訊息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        messageDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        messageDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(messageDiv);
        
        // 自動移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 5000);
    }

    /**
     * 關閉模態框
     */
    closeModal(modalId) {
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            let modal = bootstrap.Modal.getInstance(modalElement);
            if (!modal) {
                // 如果模態框實例不存在，創建一個新的
                modal = new bootstrap.Modal(modalElement);
            }
            modal.hide();
        }
    }

    /**
     * 重置模態框表單
     */
    resetModalForms(modal) {
        const forms = modal.querySelectorAll('form');
        forms.forEach(form => {
            form.reset();
            this.clearErrors(form);
        });
    }

    /**
     * 檢查是否已登入
     */
    isUserLoggedIn() {
        return this.isLoggedIn;
    }

    /**
     * 獲取翻譯文字
     */
    getText(key) {
        if (window.LanguageManager && typeof window.LanguageManager.getText === 'function') {
            return window.LanguageManager.getText(key);
        }
        return key; // 如果沒有翻譯系統，返回key本身
    }

    /**
     * 獲取Cookie值
     */
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    /**
     * 觸發登入成功事件
     */
    triggerLoginEvent() {
        console.log('triggerLoginEvent called');

        // 觸發自定義事件
        const loginEvent = new CustomEvent('userLoggedIn', {
            detail: {
                user: this.currentUser,
                isLoggedIn: this.isLoggedIn
            }
        });
        document.dispatchEvent(loginEvent);
        console.log('userLoggedIn event dispatched');

        // 通知其他組件
        if (window.presetManager && typeof window.presetManager.onUserLogin === 'function') {
            console.log('Calling presetManager.onUserLogin');
            window.presetManager.onUserLogin(this.currentUser);
        }

        if (window.ConfigManager && typeof window.ConfigManager.onUserLogin === 'function') {
            window.ConfigManager.onUserLogin(this.currentUser);
        }
    }

    /**
     * 檢查註冊狀態
     */
    async checkRegistrationStatus() {
        try {
            const response = await fetch('php/admin.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'check_registration_status'
                })
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    const registerBtn = document.getElementById('registerBtn');
                    if (registerBtn) {
                        registerBtn.style.display = result.data.registration_enabled ? 'inline-block' : 'none';
                    }
                }
            }
        } catch (error) {
            console.error('Failed to check registration status:', error);
        }
    }

    /**
     * 獲取當前用戶
     */
    getCurrentUser() {
        return this.currentUser;
    }
}

// 全局認證管理器實例
let authManager;

// 當DOM載入完成時初始化
document.addEventListener('DOMContentLoaded', function() {
    authManager = new AuthManager();
    
    // 將認證管理器添加到全局作用域以便其他腳本使用
    window.authManager = authManager;
});

// 全局函數供HTML onclick事件使用
function showLoginModal() {
    if (window.authManager) {
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.style.display = 'block';
        }
    }
}

function showRegisterModal() {
    if (window.authManager) {
        const modal = document.getElementById('registerModal');
        if (modal) {
            modal.style.display = 'block';
        }
    }
}

function showUserSettings() {
    if (window.authManager) {
        const modal = document.getElementById('userSettingsModal');
        if (modal) {
            window.authManager.populateUserSettingsForm();
            modal.style.display = 'block';
        }
    }
}

function showUserSettingsModal() {
    showUserSettings();
}

function updateProfile() {
    if (window.authManager) {
        const form = document.getElementById('profileForm');
        if (form) {
            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
            form.dispatchEvent(submitEvent);
        }
    }
}

function changePassword() {
    if (window.authManager) {
        const form = document.getElementById('changePasswordForm');
        if (form) {
            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
            form.dispatchEvent(submitEvent);
        }
    }
}

function handleLogout() {
    if (window.authManager) {
        window.authManager.handleLogout();
    }
}

function performLogin() {
    // 確保AuthManager已初始化
    if (!window.authManager) {
        window.authManager = new AuthManager();
    }

    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        // 創建一個模擬的事件對象
        const mockEvent = {
            preventDefault: () => {},
            target: loginForm
        };
        window.authManager.handleLogin(mockEvent);
    }
}

function performRegister() {
    if (!window.authManager) {
        window.authManager = new AuthManager();
    }

    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        const mockEvent = {
            preventDefault: () => {},
            target: registerForm
        };
        window.authManager.handleRegister(mockEvent);
    }
}

function performForgotPassword() {
    if (!window.authManager) {
        window.authManager = new AuthManager();
    }

    const forgotPasswordForm = document.getElementById('forgotPasswordForm');
    if (forgotPasswordForm) {
        const mockEvent = {
            preventDefault: () => {},
            target: forgotPasswordForm
        };
        window.authManager.handleForgotPassword(mockEvent);
    }
}

function showForgotPasswordModal() {
    if (window.authManager) {
        const modal = document.getElementById('forgotPasswordModal');
        if (modal) {
            modal.style.display = 'block';
        }
    }
}

// 添加logout函數
function logout() {
    if (window.authManager) {
        window.authManager.handleLogout();
    }
}

// 將函數添加到全局作用域
window.showLoginModal = showLoginModal;
window.showRegisterModal = showRegisterModal;
window.showUserSettings = showUserSettings;
window.showUserSettingsModal = showUserSettingsModal;
window.updateProfile = updateProfile;
window.changePassword = changePassword;
window.handleLogout = handleLogout;
window.logout = logout;
window.performLogin = performLogin;
window.performRegister = performRegister;
window.performForgotPassword = performForgotPassword;
window.showForgotPasswordModal = showForgotPasswordModal;

// 導出認證管理器類
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}

// 初始化認證管理器
document.addEventListener('DOMContentLoaded', () => {
    // 延遲初始化，確保在main.js之後執行
    setTimeout(() => {
        if (!window.authManager) {
            window.authManager = new AuthManager();
        }
    }, 200);
});