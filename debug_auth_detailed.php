<?php
// Start session
session_start();

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'php/Response.php';
require_once 'php/UserManager.php';

echo "Debug: Starting auth test..." . PHP_EOL;

try {
    $userManager = new UserManager();
    echo "Debug: UserManager created successfully" . PHP_EOL;
    
    // Simulate the login request
    $username = 'testuser';
    $password = 'password123';
    
    echo "Debug: Testing login with username='$username', password='$password'" . PHP_EOL;
    
    $result = $userManager->login($username, $password);
    
    echo "Debug: Login result: " . json_encode($result, JSON_PRETTY_PRINT) . PHP_EOL;
    
    if ($result['success']) {
        echo "Debug: Login successful, setting session..." . PHP_EOL;
        $_SESSION['user'] = $result['user'];
        $_SESSION['is_logged_in'] = true;
        echo "Debug: Session set successfully" . PHP_EOL;
    } else {
        echo "Debug: Login failed: " . $result['message'] . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "Debug: Exception occurred: " . $e->getMessage() . PHP_EOL;
    echo "Debug: Stack trace: " . $e->getTraceAsString() . PHP_EOL;
}
?>
