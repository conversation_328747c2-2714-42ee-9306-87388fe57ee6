<?php
/**
 * 更新收據的付款方式
 * KMS PC Receipt Maker
 */

// 啟用錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';
require_once 'UserManager.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('只允許POST請求', 405);
}

// 檢查用戶認證
$userManager = new UserManager();
$sessionToken = $_COOKIE['session_token'] ?? null;

if (!$sessionToken || !$userManager->validateSession($sessionToken)) {
    Response::error('未授權訪問，請先登入', 401);
    exit;
}

try {
    // 記錄請求信息
    error_log('Update payment method request received');

    // 獲取JSON數據
    $input = file_get_contents('php://input');
    error_log('Raw input: ' . $input);

    $data = json_decode($input, true);
    error_log('Decoded data: ' . print_r($data, true));

    if (!$data) {
        Response::error('無效的JSON數據');
    }

    // 驗證必需字段
    if (!isset($data['receipt_id']) || $data['receipt_id'] === '') {
        error_log('Missing receipt_id in data: ' . print_r($data, true));
        Response::error('收據ID不能為空');
    }

    if (!isset($data['payment_method']) || $data['payment_method'] === '') {
        error_log('Missing payment_method in data: ' . print_r($data, true));
        Response::error('付款方式不能為空');
    }

    // 驗證付款方式是否有效
    $validPaymentMethods = ['Cash', 'Venmo', 'Zelle', 'Square', 'Stripe'];
    if (!in_array($data['payment_method'], $validPaymentMethods)) {
        Response::error('無效的付款方式: ' . $data['payment_method']);
    }

    // 清理輸入數據
    $receiptId = intval($data['receipt_id']);
    $paymentMethod = $data['payment_method'];

    error_log("Processing update for receipt ID: $receiptId, payment method: $paymentMethod");

    $db = new DatabaseMySQLi();

    // 檢查收據是否存在
    $checkSql = "SELECT id, payment_method FROM receipts WHERE id = ?";
    $receipt = $db->fetch($checkSql, [$receiptId]);

    if (!$receipt) {
        Response::error('收據不存在，ID: ' . $receiptId);
    }

    error_log("Found receipt: " . print_r($receipt, true));

    // 更新付款方式 - 使用直接的 mysqli 連接
    try {
        error_log("Attempting to update payment method for receipt $receiptId to $paymentMethod");

        // 使用DatabaseMySQLi的execute方法
        $updateSql = "UPDATE receipts SET payment_method = ?, updated_at = NOW() WHERE id = ?";
        $affectedRows = $db->execute($updateSql, [$paymentMethod, $receiptId]);

        error_log("Update successful, affected rows: $affectedRows");

        Response::success([
            'receipt_id' => $receiptId,
            'payment_method' => $paymentMethod,
            'affected_rows' => $affectedRows
        ], '付款方式更新成功');

    } catch (Exception $e) {
        error_log("Update exception: " . $e->getMessage());
        Response::error('更新付款方式失敗: ' . $e->getMessage());
    }
    
} catch (Exception $e) {
    error_log('Update payment method error: ' . $e->getMessage());
    Response::error('更新付款方式失敗: ' . $e->getMessage());
}
