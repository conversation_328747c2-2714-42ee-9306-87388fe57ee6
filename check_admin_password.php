<?php
require_once 'php/config.php';
require_once 'php/DatabaseMySQLi.php';

echo "Checking admin password hash..." . PHP_EOL;

try {
    $db = new DatabaseMySQLi();
    
    // Get admin user
    $users = $db->fetchAll("SELECT username, password_hash FROM users WHERE username = 'admin'");
    if (!empty($users)) {
        $admin = $users[0];
        echo "Admin user found" . PHP_EOL;
        echo "Password hash: " . $admin['password_hash'] . PHP_EOL;
        
        // Test common passwords
        $passwords = [
            'admin',
            'password',
            'password123',
            'admin123',
            '123456'
        ];
        
        echo PHP_EOL . "Testing passwords:" . PHP_EOL;
        foreach ($passwords as $password) {
            $result = password_verify($password, $admin['password_hash']);
            echo "Password: '$password' - " . ($result ? 'MATCH' : 'NO MATCH') . PHP_EOL;
        }
    } else {
        echo "Admin user not found" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
?>
