<?php
/**
 * 數據庫初始化腳本
 * 自動創建所有必要的表和數據
 */

require_once 'php/config.php';

// 設置錯誤處理
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

header('Content-Type: text/html; charset=utf-8');

echo "<h2>數據庫初始化</h2>";

try {
    // 連接到 MySQL（不指定數據庫）
    $connection = new mysqli(DB_HOST, DB_USER, DB_PASS);
    
    if ($connection->connect_error) {
        throw new Exception("Connection failed: " . $connection->connect_error);
    }
    
    echo "<p style='color: green;'>✓ MySQL 連接成功</p>";
    
    // 創建數據庫
    $sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    if ($connection->query($sql)) {
        echo "<p style='color: green;'>✓ 數據庫 '" . DB_NAME . "' 創建成功</p>";
    } else {
        throw new Exception("創建數據庫失敗: " . $connection->error);
    }
    
    // 選擇數據庫
    $connection->select_db(DB_NAME);
    
    // 創建用戶表
    $sql = "
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        is_active BOOLEAN DEFAULT TRUE,
        failed_login_attempts INT DEFAULT 0,
        locked_until TIMESTAMP NULL,
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($connection->query($sql)) {
        echo "<p style='color: green;'>✓ 用戶表創建成功</p>";
    } else {
        throw new Exception("創建用戶表失敗: " . $connection->error);
    }
    
    // 創建用戶會話表
    $sql = "
    CREATE TABLE IF NOT EXISTS user_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        user_agent TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_session_token (session_token),
        INDEX idx_user_id (user_id),
        INDEX idx_expires (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($connection->query($sql)) {
        echo "<p style='color: green;'>✓ 用戶會話表創建成功</p>";
    } else {
        throw new Exception("創建用戶會話表失敗: " . $connection->error);
    }
    
    // 創建收據表
    $sql = "
    CREATE TABLE IF NOT EXISTS receipts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        receipt_number VARCHAR(50) UNIQUE NOT NULL,
        customer_name VARCHAR(100) NOT NULL,
        customer_phone VARCHAR(20),
        customer_email VARCHAR(100),
        customer_address TEXT,
        subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        payment_method ENUM('Cash', 'Venmo', 'Zelle', 'Square', 'Stripe') DEFAULT 'Cash',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_receipt_number (receipt_number),
        INDEX idx_customer_name (customer_name),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($connection->query($sql)) {
        echo "<p style='color: green;'>✓ 收據表創建成功</p>";
    } else {
        throw new Exception("創建收據表失敗: " . $connection->error);
    }
    
    // 創建收據項目表
    $sql = "
    CREATE TABLE IF NOT EXISTS receipt_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        receipt_id INT NOT NULL,
        item_name VARCHAR(200) NOT NULL,
        item_description TEXT,
        category VARCHAR(50),
        quantity INT NOT NULL DEFAULT 1,
        unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        original_price DECIMAL(10,2) DEFAULT NULL,
        special_price DECIMAL(10,2) DEFAULT NULL,
        discount_percent INT DEFAULT 0,
        hide_price BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (receipt_id) REFERENCES receipts(id) ON DELETE CASCADE,
        INDEX idx_receipt_id (receipt_id),
        INDEX idx_category (category)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($connection->query($sql)) {
        echo "<p style='color: green;'>✓ 收據項目表創建成功</p>";
    } else {
        throw new Exception("創建收據項目表失敗: " . $connection->error);
    }
    
    // 創建電腦零件表
    $sql = "
    CREATE TABLE IF NOT EXISTS pc_parts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(200) NOT NULL,
        category VARCHAR(50) NOT NULL,
        description TEXT,
        default_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        special_price DECIMAL(10,2) DEFAULT NULL,
        discount_percent INT DEFAULT 0,
        hide_price BOOLEAN DEFAULT FALSE,
        sort_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_category (category),
        INDEX idx_name (name),
        INDEX idx_sort_order (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($connection->query($sql)) {
        echo "<p style='color: green;'>✓ 電腦零件表創建成功</p>";
    } else {
        throw new Exception("創建電腦零件表失敗: " . $connection->error);
    }
    
    // 創建收據配置表
    $sql = "
    CREATE TABLE IF NOT EXISTS receipt_configurations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        customer_name VARCHAR(100),
        customer_phone VARCHAR(20),
        customer_email VARCHAR(100),
        customer_address TEXT,
        payment_method VARCHAR(20) DEFAULT 'Cash',
        items JSON,
        discount_amount DECIMAL(10,2) DEFAULT 0.00,
        tax_rate DECIMAL(5,4) DEFAULT 0.0000,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($connection->query($sql)) {
        echo "<p style='color: green;'>✓ 收據配置表創建成功</p>";
    } else {
        throw new Exception("創建收據配置表失敗: " . $connection->error);
    }
    
    // 創建默認管理員用戶
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $sql = "INSERT IGNORE INTO users (username, email, password_hash, full_name) VALUES ('admin', '<EMAIL>', ?, 'Administrator')";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param('s', $adminPassword);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✓ 默認管理員用戶創建成功 (用戶名: admin, 密碼: admin123)</p>";
    } else {
        echo "<p style='color: orange;'>⚠ 管理員用戶可能已存在</p>";
    }
    
    $stmt->close();
    $connection->close();
    
    echo "<h3 style='color: green;'>數據庫初始化完成！</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 錯誤: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='check_database.php'>檢查數據庫狀態</a> | <a href='index.php'>返回主頁</a></p>";
?>