<?php
require_once 'config.php';
require_once 'DatabaseMySQLi.php';
require_once 'SystemSettings.php';

/**
 * 管理員管理類
 */
class AdminManager {
    private $db;
    private $settings;
    
    public function __construct() {
        try {
            $this->db = new DatabaseMySQLi();
        } catch (Exception $e) {
            // 如果 MySQL 連接失敗，回退到文件數據庫
            require_once 'FileDatabase.php';
            $this->db = new FileDatabase();
        }
        
        try {
            $this->settings = new SystemSettings();
        } catch (Exception $e) {
            // SystemSettings 初始化失敗，使用默認設置
            $this->settings = null;
        }
    }
    
    /**
     * 檢查用戶是否為管理員
     */
    public function isAdmin($userId) {
        try {
            $sql = "SELECT role FROM users WHERE id = ? AND is_active = TRUE";
            $result = $this->db->fetchAll($sql, [$userId]);
            
            return !empty($result) && $result[0]['role'] === 'admin';
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 獲取待審核的用戶列表
     */
    public function getPendingUsers() {
        try {
            $sql = "SELECT id, username, email, full_name, created_at 
                    FROM users 
                    WHERE status = 'pending' AND is_active = TRUE 
                    ORDER BY created_at ASC";
            
            return $this->db->fetchAll($sql);
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * 獲取所有用戶列表（分頁）
     */
    public function getAllUsers($page = 1, $limit = 20, $filters = []) {
        try {
            $offset = ($page - 1) * $limit;
            $whereConditions = ['is_active = TRUE'];
            $params = [];
            
            // 添加過濾條件
            if (!empty($filters['status'])) {
                $whereConditions[] = 'status = ?';
                $params[] = $filters['status'];
            }
            
            if (!empty($filters['role'])) {
                $whereConditions[] = 'role = ?';
                $params[] = $filters['role'];
            }
            
            if (!empty($filters['search'])) {
                $whereConditions[] = '(username LIKE ? OR email LIKE ? OR full_name LIKE ?)';
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            $whereClause = implode(' AND ', $whereConditions);
            
            // 獲取總數
            $countSql = "SELECT COUNT(*) as total FROM users WHERE $whereClause";
            $countResult = $this->db->fetchAll($countSql, $params);
            $total = $countResult[0]['total'];
            
            // 獲取用戶列表
            $sql = "SELECT id, username, email, full_name, role, status, created_at, last_login 
                    FROM users 
                    WHERE $whereClause 
                    ORDER BY created_at DESC 
                    LIMIT ? OFFSET ?";
            
            $params[] = $limit;
            $params[] = $offset;
            
            $users = $this->db->fetchAll($sql, $params);
            
            return [
                'users' => $users,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'totalPages' => ceil($total / $limit)
            ];
        } catch (Exception $e) {
            return [
                'users' => [],
                'total' => 0,
                'page' => $page,
                'limit' => $limit,
                'totalPages' => 0
            ];
        }
    }
    
    /**
     * 批准用戶
     */
    public function approveUser($userId, $adminUserId) {
        try {
            $sql = "UPDATE users SET status = 'approved', updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            $this->db->execute($sql, [$userId]);
            
            // 記錄管理員操作
            $this->logAdminAction($adminUserId, 'approve_user', $userId, "Approved user ID: $userId");
            
            return ['success' => true, 'message' => 'User approved successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to approve user: ' . $e->getMessage()];
        }
    }
    
    /**
     * 拒絕用戶
     */
    public function rejectUser($userId, $adminUserId, $reason = '') {
        try {
            $sql = "UPDATE users SET status = 'rejected', updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            $this->db->execute($sql, [$userId]);
            
            // 記錄管理員操作
            $this->logAdminAction($adminUserId, 'reject_user', $userId, "Rejected user ID: $userId. Reason: $reason");
            
            return ['success' => true, 'message' => 'User rejected successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to reject user: ' . $e->getMessage()];
        }
    }
    
    /**
     * 暫停用戶
     */
    public function suspendUser($userId, $adminUserId, $reason = '') {
        try {
            $sql = "UPDATE users SET status = 'suspended', updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            $this->db->execute($sql, [$userId]);
            
            // 記錄管理員操作
            $this->logAdminAction($adminUserId, 'suspend_user', $userId, "Suspended user ID: $userId. Reason: $reason");
            
            return ['success' => true, 'message' => 'User suspended successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to suspend user: ' . $e->getMessage()];
        }
    }
    
    /**
     * 恢復用戶
     */
    public function restoreUser($userId, $adminUserId) {
        try {
            $sql = "UPDATE users SET status = 'approved', updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            $this->db->execute($sql, [$userId]);
            
            // 記錄管理員操作
            $this->logAdminAction($adminUserId, 'restore_user', $userId, "Restored user ID: $userId");
            
            return ['success' => true, 'message' => 'User restored successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to restore user: ' . $e->getMessage()];
        }
    }
    
    /**
     * 記錄管理員操作
     */
    private function logAdminAction($adminUserId, $action, $targetUserId = null, $description = '') {
        try {
            $sql = "INSERT INTO admin_logs (admin_user_id, action, target_user_id, description, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $this->db->execute($sql, [
                $adminUserId,
                $action,
                $targetUserId,
                $description,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (Exception $e) {
            // 記錄失敗不影響主要操作
        }
    }
    
    /**
     * 獲取管理員操作日誌
     */
    public function getAdminLogs($page = 1, $limit = 50) {
        try {
            $offset = ($page - 1) * $limit;
            
            $sql = "SELECT al.*, au.username as admin_username, tu.username as target_username 
                    FROM admin_logs al 
                    LEFT JOIN users au ON al.admin_user_id = au.id 
                    LEFT JOIN users tu ON al.target_user_id = tu.id 
                    ORDER BY al.created_at DESC 
                    LIMIT ? OFFSET ?";
            
            return $this->db->fetchAll($sql, [$limit, $offset]);
        } catch (Exception $e) {
            return [];
        }
    }
}
?>
