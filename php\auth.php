<?php
/**
 * 用戶認證API端點
 */

// 啟用會話
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'Response.php';
require_once 'UserManager.php';

try {
    $userManager = new UserManager();
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? $_GET['action'] ?? '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($action) {
        case 'login':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }
            
            $username = $input['username'] ?? '';
            $password = $input['password'] ?? '';
            
            if (empty($username) || empty($password)) {
                Response::error('Username and password cannot be empty');
            }
            
            $result = $userManager->login($username, $password);
            
            if ($result['success']) {
                // 設置會話
                $_SESSION['user'] = $result['user'];
                $_SESSION['is_logged_in'] = true;
                
                Response::success([
                    'success' => true,
                    'user' => $result['user']
                ]);
            } else {
                Response::error($result['message']);
            }
            break;
            
        case 'register':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }
            
            $username = $input['username'] ?? '';
            $email = $input['email'] ?? '';
            $password = $input['password'] ?? '';
            $fullName = $input['full_name'] ?? '';
            
            if (empty($username) || empty($email) || empty($password)) {
                Response::error('Username, email and password cannot be empty');
            }
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                Response::error('Invalid email format');
            }
            
            $result = $userManager->register($username, $email, $password, $fullName);
            
            if ($result['success']) {
                Response::success($result);
            } else {
                Response::error($result['message']);
            }
            break;
            
        case 'logout':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }
            
            // 銷毀會話
            session_unset();
            session_destroy();
            
            Response::success(['success' => true, 'message' => 'Logged out successfully']);
            break;
            
        case 'checkSession':
        case 'validate':
        case 'validate_session':
            // 支持GET和POST方法
            if ($method !== 'GET' && $method !== 'POST') {
                Response::error('Only GET or POST requests allowed', 405);
            }
            
            if (isset($_SESSION['is_logged_in']) && $_SESSION['is_logged_in'] === true) {
                Response::success([
                    'valid' => true,
                    'user' => $_SESSION['user']
                ]);
            } else {
                Response::error('Invalid or expired session', 401);
            }
            break;
            
        case 'change_password':
            if ($method !== 'POST') {
                Response::error('只允許POST請求', 405);
            }
            
            if (!isset($_SESSION['is_logged_in']) || $_SESSION['is_logged_in'] !== true) {
                Response::error('會話無效或已過期', 401);
            }
            
            $currentPassword = $input['current_password'] ?? '';
            $newPassword = $input['new_password'] ?? '';
            
            if (empty($currentPassword) || empty($newPassword)) {
                Response::error('當前密碼和新密碼不能為空');
            }
            
            $result = $userManager->changePassword(
                $_SESSION['user']['id'],
                $currentPassword,
                $newPassword
            );
            
            if ($result['success']) {
                Response::success($result);
            } else {
                Response::error($result['message']);
            }
            break;
            
        case 'update_profile':
            if ($method !== 'POST') {
                Response::error('只允許POST請求', 405);
            }
            
            if (!isset($_SESSION['is_logged_in']) || $_SESSION['is_logged_in'] !== true) {
                Response::error('會話無效或已過期', 401);
            }
            
            $email = $input['email'] ?? '';
            $fullName = $input['full_name'] ?? '';
            
            if (empty($email)) {
                Response::error('郵箱不能為空');
            }
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                Response::error('郵箱格式不正確');
            }
            
            $result = $userManager->updateProfile(
                $_SESSION['user']['id'],
                $email,
                $fullName
            );
            
            if ($result['success']) {
                // 更新會話中的用戶資訊
                $_SESSION['user']['email'] = $email;
                $_SESSION['user']['full_name'] = $fullName;
                Response::success($result);
            } else {
                Response::error($result['message']);
            }
            break;
            
        default:
            Response::error('Invalid action', 400);
    }
    
} catch (Exception $e) {
    Response::error('System error: ' . $e->getMessage(), 500);
}
?>