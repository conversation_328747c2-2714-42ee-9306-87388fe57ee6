<?php
/**
 * API響應處理類
 * KMS PC Receipt Maker
 */

class Response {
    
    /**
     * 發送成功響應
     */
    public static function success($data = null, $message = '操作成功') {
        self::sendJson([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 發送錯誤響應
     */
    public static function error($message = '操作失敗', $code = 400, $details = null) {
        http_response_code($code);
        self::sendJson([
            'success' => false,
            'message' => $message,
            'error_code' => $code,
            'details' => $details,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 發送JSON響應
     */
    private static function sendJson($data) {
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
        
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
    
    /**
     * 驗證必需參數
     */
    public static function validateRequired($data, $required_fields) {
        $missing = [];

        foreach ($required_fields as $field) {
            if (!isset($data[$field])) {
                $missing[] = $field;
            } else {
                // 檢查是否為空值
                $value = $data[$field];
                if (is_string($value) && empty(trim($value))) {
                    $missing[] = $field;
                } else if (is_array($value) && empty($value)) {
                    $missing[] = $field;
                } else if ($value === null || $value === '') {
                    $missing[] = $field;
                }
            }
        }

        if (!empty($missing)) {
            self::error('缺少必需參數: ' . implode(', ', $missing), 400);
        }

        return true;
    }
    
    /**
     * 清理輸入數據
     */
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            $cleaned = [];
            foreach ($data as $key => $value) {
                $cleaned[$key] = self::sanitizeInput($value);
            }
            return $cleaned;
        }
        
        if (is_string($data)) {
            return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
        }
        
        // 對於數字、布爾值等其他類型，直接返回
        return $data;
    }
    
    /**
     * 驗證郵箱格式
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * 驗證手機號格式
     */
    public static function validatePhone($phone) {
        // 簡單的手機號驗證，可根據需要調整
        return preg_match('/^[\d\-\+\(\)\s]+$/', $phone);
    }
}
?>
