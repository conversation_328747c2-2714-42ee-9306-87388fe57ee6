<?php
require_once 'php/config.php';
require_once 'php/SystemSettings.php';

echo "Testing SystemSettings..." . PHP_EOL;

try {
    $settings = new SystemSettings();
    echo "SystemSettings created successfully" . PHP_EOL;
    
    // Test some methods
    $registrationEnabled = $settings->isRegistrationEnabled();
    echo "Registration enabled: " . ($registrationEnabled ? 'true' : 'false') . PHP_EOL;
    
    $requireApproval = $settings->requireAdminApproval();
    echo "Require admin approval: " . ($requireApproval ? 'true' : 'false') . PHP_EOL;
    
} catch (Exception $e) {
    echo "SystemSettings failed: " . $e->getMessage() . PHP_EOL;
    echo "Stack trace: " . $e->getTraceAsString() . PHP_EOL;
}
?>
