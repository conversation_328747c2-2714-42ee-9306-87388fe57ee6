<?php
// Simulate POST request to auth.php
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['CONTENT_TYPE'] = 'application/json';

// Simulate JSON input
$input = json_encode([
    'action' => 'login',
    'username' => 'testuser',
    'password' => 'password123'
]);

// Mock php://input
$temp = tmpfile();
fwrite($temp, $input);
rewind($temp);

// Override file_get_contents for php://input
function file_get_contents_override($filename) {
    global $temp;
    if ($filename === 'php://input') {
        return stream_get_contents($temp);
    }
    return file_get_contents($filename);
}

echo "Testing auth.php directly..." . PHP_EOL;
echo "Input: " . $input . PHP_EOL;

// Start output buffering to capture auth.php output
ob_start();

try {
    // Include auth.php
    include 'php/auth.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . PHP_EOL;
}

$output = ob_get_clean();
echo "Output: " . $output . PHP_EOL;

fclose($temp);
?>
