<?php
/**
 * 用戶預設項目管理API
 * 支持用戶隔離的預設項目CRUD操作
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'UserManager.php';
require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

try {
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true) ?? [];
    $action = $input['action'] ?? $_GET['action'] ?? '';
    
    $userManager = new UserManager();
    $db = new DatabaseMySQLi();
    
    // 驗證用戶會話
    $sessionToken = $_COOKIE['session_token'] ?? null;
    $currentUser = null;
    
    if ($sessionToken && $userManager->validateSession($sessionToken)) {
        $sessionInfo = $userManager->getSessionInfo($sessionToken);
        if ($sessionInfo) {
            $currentUser = $userManager->getUserById($sessionInfo['user_id']);
        }
    }
    
    if (!$currentUser) {
        Response::error('Authentication required', 401);
        exit;
    }
    
    switch ($action) {
        case 'get_presets':
            if ($method !== 'GET') {
                Response::error('Only GET requests allowed', 405);
            }
            
            $category = $_GET['category'] ?? '';
            $whereClause = 'WHERE user_id = ? AND is_active = 1';
            $params = [$currentUser['id']];
            
            if (!empty($category)) {
                $whereClause .= ' AND category = ?';
                $params[] = $category;
            }
            
            $sql = "SELECT * FROM pc_parts $whereClause ORDER BY sort_order ASC, category, name";
            $presets = $db->fetchAll($sql, $params);
            
            Response::success($presets);
            break;
            
        case 'save_preset':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }
            
            $name = trim($input['name'] ?? '');
            $category = trim($input['category'] ?? '');
            $description = trim($input['description'] ?? '');
            $defaultPrice = $input['default_price'] ?? 0.00;
            $originalPrice = $input['original_price'] ?? null;
            $specialPrice = $input['special_price'] ?? null;
            $id = $input['id'] ?? null;
            
            if (empty($name) || empty($category)) {
                Response::error('Name and category are required');
            }
            
            if ($id) {
                // 更新現有預設（確保只能更新自己的預設）
                $sql = "UPDATE pc_parts SET 
                        name = ?, category = ?, description = ?, 
                        default_price = ?, original_price = ?, special_price = ?,
                        updated_at = CURRENT_TIMESTAMP
                        WHERE id = ? AND user_id = ?";
                
                $db->execute($sql, [
                    $name, $category, $description,
                    $defaultPrice, $originalPrice, $specialPrice,
                    $id, $currentUser['id']
                ]);
                
                Response::success(['id' => $id], 'Preset updated successfully');
            } else {
                // 創建新預設
                $sql = "INSERT INTO pc_parts 
                        (user_id, name, category, description, default_price, original_price, special_price, is_active, created_at, updated_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";
                
                $db->execute($sql, [
                    $currentUser['id'], $name, $category, $description,
                    $defaultPrice, $originalPrice, $specialPrice
                ]);
                
                $newId = $db->lastInsertId();
                Response::success(['id' => $newId], 'Preset created successfully');
            }
            break;
            
        case 'delete_preset':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }
            
            $id = $input['id'] ?? 0;
            if (!$id) {
                Response::error('Preset ID is required');
            }
            
            // 軟刪除（確保只能刪除自己的預設）
            $sql = "UPDATE pc_parts SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?";
            $db->execute($sql, [$id, $currentUser['id']]);
            
            Response::success(null, 'Preset deleted successfully');
            break;
            
        case 'get_categories':
            if ($method !== 'GET') {
                Response::error('Only GET requests allowed', 405);
            }
            
            $sql = "SELECT DISTINCT category FROM pc_parts WHERE user_id = ? AND is_active = 1 ORDER BY category";
            $categories = $db->fetchAll($sql, [$currentUser['id']]);
            
            $categoryList = array_column($categories, 'category');
            Response::success($categoryList);
            break;
            
        case 'import_global_presets':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }
            
            // 導入全局預設項目到用戶的個人預設
            $sql = "INSERT INTO pc_parts (user_id, name, category, description, default_price, original_price, special_price, is_active, created_at, updated_at)
                    SELECT ?, name, category, description, default_price, original_price, special_price, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                    FROM pc_parts 
                    WHERE user_id IS NULL AND is_active = 1
                    AND NOT EXISTS (
                        SELECT 1 FROM pc_parts p2 
                        WHERE p2.user_id = ? AND p2.name = pc_parts.name AND p2.category = pc_parts.category
                    )";
            
            $db->execute($sql, [$currentUser['id'], $currentUser['id']]);
            
            Response::success(null, 'Global presets imported successfully');
            break;

        case 'update_sort_order':
            if ($method !== 'POST') {
                Response::error('Only POST requests allowed', 405);
            }

            $items = $input['items'] ?? [];
            if (empty($items)) {
                Response::error('Items array is required');
            }

            // 批量更新排序
            foreach ($items as $item) {
                $id = $item['id'] ?? 0;
                $sortOrder = $item['sort_order'] ?? 0;

                if ($id > 0) {
                    $sql = "UPDATE pc_parts SET sort_order = ? WHERE id = ? AND user_id = ?";
                    $db->execute($sql, [$sortOrder, $id, $currentUser['id']]);
                }
            }

            Response::success(null, 'Sort order updated successfully');
            break;

        default:
            Response::error('Invalid action', 400);
            break;
    }
    
} catch (Exception $e) {
    Response::error('System error: ' . $e->getMessage(), 500);
}
?>
