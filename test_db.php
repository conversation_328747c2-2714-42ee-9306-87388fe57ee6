<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=kms_receipt_maker', 'root', '');
    echo 'Database connection successful' . PHP_EOL;
    
    // Test if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo 'Users table exists' . PHP_EOL;
        
        // Test if we can query users
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch();
        echo 'Users count: ' . $result['count'] . PHP_EOL;
    } else {
        echo 'Users table does not exist' . PHP_EOL;
    }
} catch (Exception $e) {
    echo 'Database connection failed: ' . $e->getMessage() . PHP_EOL;
}
?>
