<?php
require_once 'config.php';
require_once 'DatabaseMySQLi.php';

/**
 * 系統設置管理類
 */
class SystemSettings {
    private $db;
    
    public function __construct() {
        try {
            $this->db = new DatabaseMySQLi();
        } catch (Exception $e) {
            // MySQL 連接失敗，回退到文件數據庫
            require_once 'FileDatabase.php';
            $this->db = new FileDatabase();
        }
    }
    
    /**
     * 獲取系統設置
     */
    public function getSetting($key, $default = null) {
        try {
            // 檢查數據庫類型
            if ($this->db instanceof FileDatabase) {
                // 使用文件數據庫的 getSetting 方法
                return $this->db->getSetting($key, $default);
            } else {
                // 使用 MySQL 數據庫
                $sql = "SELECT setting_value FROM system_settings WHERE setting_key = ?";
                $result = $this->db->fetchAll($sql, [$key]);
                
                if (!empty($result)) {
                    $value = $result[0]['setting_value'];
                    // 嘗試解析布爾值
                    if ($value === 'true') return true;
                    if ($value === 'false') return false;
                    return $value;
                }
                
                return $default;
            }
        } catch (Exception $e) {
            return $default;
        }
    }
    
    /**
     * 設置系統設置
     */
    public function setSetting($key, $value, $description = null) {
        try {
            // 檢查數據庫類型
            if ($this->db instanceof FileDatabase) {
                // 使用文件數據庫的 setSetting 方法
                return $this->db->setSetting($key, $value, $description);
            } else {
                // 使用 MySQL 數據庫
                // 轉換布爾值為字符串
                if (is_bool($value)) {
                    $value = $value ? 'true' : 'false';
                }
                
                $sql = "INSERT INTO system_settings (setting_key, setting_value, description) 
                        VALUES (?, ?, ?) 
                        ON DUPLICATE KEY UPDATE 
                        setting_value = VALUES(setting_value),
                        description = COALESCE(VALUES(description), description),
                        updated_at = CURRENT_TIMESTAMP";
                
                $this->db->execute($sql, [$key, $value, $description]);
                return true;
            }
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 獲取所有系統設置
     */
    public function getAllSettings() {
        try {
            $sql = "SELECT setting_key, setting_value, description FROM system_settings ORDER BY setting_key";
            $result = $this->db->fetchAll($sql);
            
            $settings = [];
            foreach ($result as $row) {
                $value = $row['setting_value'];
                // 轉換布爾值
                if ($value === 'true') $value = true;
                if ($value === 'false') $value = false;
                
                $settings[$row['setting_key']] = [
                    'value' => $value,
                    'description' => $row['description']
                ];
            }
            
            return $settings;
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * 檢查註冊是否開啟
     */
    public function isRegistrationEnabled() {
        return $this->getSetting('registration_enabled', true);
    }
    
    /**
     * 檢查是否需要管理員審核
     */
    public function requireAdminApproval() {
        return $this->getSetting('require_admin_approval', true);
    }
    
    /**
     * 獲取默認用戶角色
     */
    public function getDefaultUserRole() {
        return $this->getSetting('default_user_role', 'member');
    }
    
    /**
     * 設置註冊開關
     */
    public function setRegistrationEnabled($enabled) {
        return $this->setSetting('registration_enabled', $enabled, '是否允許新用戶註冊');
    }
    
    /**
     * 設置管理員審核要求
     */
    public function setRequireAdminApproval($required) {
        return $this->setSetting('require_admin_approval', $required, '新用戶是否需要管理員審核');
    }
}
?>
