<?php
/**
 * 數據庫檢查腳本
 * 檢查數據庫連接和表結構
 */

require_once 'php/config.php';

// 設置錯誤處理
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

header('Content-Type: text/html; charset=utf-8');

echo "<h2>數據庫連接檢查</h2>";

try {
    // 測試數據庫連接
    $connection = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($connection->connect_error) {
        throw new Exception("Connection failed: " . $connection->connect_error);
    }
    
    echo "<p style='color: green;'>✓ 數據庫連接成功</p>";
    
    // 檢查必要的表是否存在
    $tables = [
        'users',
        'user_sessions', 
        'receipts',
        'receipt_items',
        'pc_parts',
        'receipt_configurations'
    ];
    
    echo "<h3>檢查表結構:</h3>";
    
    foreach ($tables as $table) {
        $result = $connection->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✓ 表 '$table' 存在</p>";
        } else {
            echo "<p style='color: red;'>✗ 表 '$table' 不存在</p>";
        }
    }
    
    // 檢查用戶表中是否有數據
    $result = $connection->query("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p>用戶數量: {$row['count']}</p>";
    }
    
    // 檢查會話表
    $result = $connection->query("SELECT COUNT(*) as count FROM user_sessions WHERE is_active = TRUE");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p>活躍會話數量: {$row['count']}</p>";
    }
    
    $connection->close();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 錯誤: " . $e->getMessage() . "</p>";
    
    // 提供解決方案
    echo "<h3>解決方案:</h3>";
    echo "<ol>";
    echo "<li>確保 XAMPP 已啟動 MySQL 服務</li>";
    echo "<li>檢查 php/config.php 中的數據庫配置</li>";
    echo "<li>運行數據庫初始化腳本: database/setup.sql</li>";
    echo "<li>運行用戶認證腳本: database/07_user_authentication.sql</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p><a href='index.php'>返回主頁</a></p>";
?>