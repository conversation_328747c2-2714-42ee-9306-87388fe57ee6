/**
 * 管理員面板功能
 */
class AdminManager {
    constructor() {
        this.currentUser = null;
        this.isAdmin = false;
        this.init();
    }

    init() {
        // 監聽標籤切換事件
        document.addEventListener('DOMContentLoaded', () => {
            const adminTabs = document.querySelectorAll('#adminTabs button[data-bs-toggle="tab"]');
            adminTabs.forEach(tab => {
                tab.addEventListener('shown.bs.tab', (event) => {
                    const target = event.target.getAttribute('data-bs-target');
                    this.loadTabContent(target);
                });
            });
        });
    }

    /**
     * 顯示管理員面板
     */
    showAdminPanel() {
        const modal = document.getElementById('adminPanelModal');
        if (modal) {
            // 載入待審核用戶（默認標籤）
            this.loadPendingUsers();
            modal.style.display = 'block';
        }
    }

    /**
     * 載入標籤內容
     */
    loadTabContent(target) {
        switch (target) {
            case '#pending-users':
                this.loadPendingUsers();
                break;
            case '#all-users':
                this.loadAllUsers();
                break;
            case '#system-settings':
                this.loadSystemSettings();
                break;
        }
    }

    /**
     * 載入待審核用戶
     */
    async loadPendingUsers() {
        const container = document.getElementById('pendingUsersContainer');
        if (!container) return;

        try {
            const response = await this.makeAdminRequest('get_pending_users');
            if (response.success) {
                this.renderPendingUsers(response.data.users);
            } else {
                container.innerHTML = '<div class="alert alert-danger">載入失敗：' + response.message + '</div>';
            }
        } catch (error) {
            container.innerHTML = '<div class="alert alert-danger">載入失敗：網路錯誤</div>';
        }
    }

    /**
     * 渲染待審核用戶列表
     */
    renderPendingUsers(users) {
        const container = document.getElementById('pendingUsersContainer');
        if (!container) return;

        if (users.length === 0) {
            container.innerHTML = '<div class="alert alert-info">沒有待審核的用戶</div>';
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>用戶名</th><th>郵箱</th><th>全名</th><th>註冊時間</th><th>操作</th></tr></thead><tbody>';

        users.forEach(user => {
            html += `<tr>
                <td>${this.escapeHtml(user.username)}</td>
                <td>${this.escapeHtml(user.email)}</td>
                <td>${this.escapeHtml(user.full_name || '')}</td>
                <td>${new Date(user.created_at).toLocaleString()}</td>
                <td>
                    <button class="btn btn-success btn-sm me-2" onclick="adminManager.approveUser(${user.id})">
                        <span class="icon-symbol">✓</span> 批准
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="adminManager.rejectUser(${user.id})">
                        <span class="icon-symbol">✗</span> 拒絕
                    </button>
                </td>
            </tr>`;
        });

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }

    /**
     * 載入所有用戶
     */
    async loadAllUsers(page = 1) {
        const container = document.getElementById('allUsersContainer');
        if (!container) return;

        try {
            const searchInput = document.getElementById('userSearchInput');
            const statusFilter = document.getElementById('userStatusFilter');
            const roleFilter = document.getElementById('userRoleFilter');

            const params = new URLSearchParams({
                page: page,
                limit: 20,
                search: searchInput ? searchInput.value : '',
                status: statusFilter ? statusFilter.value : '',
                role: roleFilter ? roleFilter.value : ''
            });

            const response = await this.makeAdminRequest('get_all_users?' + params.toString());
            if (response.success) {
                this.renderAllUsers(response.data);
            } else {
                container.innerHTML = '<div class="alert alert-danger">載入失敗：' + response.message + '</div>';
            }
        } catch (error) {
            container.innerHTML = '<div class="alert alert-danger">載入失敗：網路錯誤</div>';
        }
    }

    /**
     * 渲染所有用戶列表
     */
    renderAllUsers(data) {
        const container = document.getElementById('allUsersContainer');
        if (!container) return;

        const { users, total, page, totalPages } = data;

        if (users.length === 0) {
            container.innerHTML = '<div class="alert alert-info">沒有找到用戶</div>';
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>用戶名</th><th>郵箱</th><th>角色</th><th>狀態</th><th>註冊時間</th><th>最後登入</th><th>操作</th></tr></thead><tbody>';

        users.forEach(user => {
            const statusBadge = this.getStatusBadge(user.status);
            const roleBadge = this.getRoleBadge(user.role);
            
            html += `<tr>
                <td>${this.escapeHtml(user.username)}</td>
                <td>${this.escapeHtml(user.email)}</td>
                <td>${roleBadge}</td>
                <td>${statusBadge}</td>
                <td>${new Date(user.created_at).toLocaleString()}</td>
                <td>${user.last_login ? new Date(user.last_login).toLocaleString() : '從未登入'}</td>
                <td>
                    ${this.getUserActionButtons(user)}
                </td>
            </tr>`;
        });

        html += '</tbody></table></div>';

        // 添加分頁
        if (totalPages > 1) {
            html += this.renderPagination(page, totalPages);
        }

        container.innerHTML = html;
    }

    /**
     * 獲取狀態徽章
     */
    getStatusBadge(status) {
        const badges = {
            'pending': '<span class="badge bg-warning">待審核</span>',
            'approved': '<span class="badge bg-success">已批准</span>',
            'rejected': '<span class="badge bg-danger">已拒絕</span>',
            'suspended': '<span class="badge bg-secondary">已暫停</span>'
        };
        return badges[status] || '<span class="badge bg-light">未知</span>';
    }

    /**
     * 獲取角色徽章
     */
    getRoleBadge(role) {
        const badges = {
            'admin': '<span class="badge bg-primary">管理員</span>',
            'member': '<span class="badge bg-info">會員</span>'
        };
        return badges[role] || '<span class="badge bg-light">未知</span>';
    }

    /**
     * 獲取用戶操作按鈕
     */
    getUserActionButtons(user) {
        let buttons = '';
        
        if (user.status === 'pending') {
            buttons += `<button class="btn btn-success btn-sm me-1" onclick="adminManager.approveUser(${user.id})">批准</button>`;
            buttons += `<button class="btn btn-danger btn-sm me-1" onclick="adminManager.rejectUser(${user.id})">拒絕</button>`;
        } else if (user.status === 'approved') {
            buttons += `<button class="btn btn-warning btn-sm me-1" onclick="adminManager.suspendUser(${user.id})">暫停</button>`;
        } else if (user.status === 'suspended') {
            buttons += `<button class="btn btn-success btn-sm me-1" onclick="adminManager.restoreUser(${user.id})">恢復</button>`;
        }
        
        return buttons;
    }

    /**
     * 渲染分頁
     */
    renderPagination(currentPage, totalPages) {
        let html = '<nav aria-label="用戶列表分頁"><ul class="pagination justify-content-center">';
        
        // 上一頁
        if (currentPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="adminManager.loadAllUsers(${currentPage - 1})">上一頁</a></li>`;
        }
        
        // 頁碼
        for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
            const active = i === currentPage ? 'active' : '';
            html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="adminManager.loadAllUsers(${i})">${i}</a></li>`;
        }
        
        // 下一頁
        if (currentPage < totalPages) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="adminManager.loadAllUsers(${currentPage + 1})">下一頁</a></li>`;
        }
        
        html += '</ul></nav>';
        return html;
    }

    /**
     * 批准用戶
     */
    async approveUser(userId) {
        if (!confirm('確定要批准這個用戶嗎？')) return;

        try {
            const response = await this.makeAdminRequest('approve_user', { user_id: userId });
            if (response.success) {
                this.showMessage('用戶已批准', 'success');
                this.loadPendingUsers();
                this.loadAllUsers();
            } else {
                this.showMessage('批准失敗：' + response.message, 'error');
            }
        } catch (error) {
            this.showMessage('批准失敗：網路錯誤', 'error');
        }
    }

    /**
     * 拒絕用戶
     */
    async rejectUser(userId) {
        const reason = prompt('請輸入拒絕原因（可選）：');
        if (reason === null) return; // 用戶取消

        try {
            const response = await this.makeAdminRequest('reject_user', { user_id: userId, reason: reason });
            if (response.success) {
                this.showMessage('用戶已拒絕', 'success');
                this.loadPendingUsers();
                this.loadAllUsers();
            } else {
                this.showMessage('拒絕失敗：' + response.message, 'error');
            }
        } catch (error) {
            this.showMessage('拒絕失敗：網路錯誤', 'error');
        }
    }

    /**
     * 暫停用戶
     */
    async suspendUser(userId) {
        const reason = prompt('請輸入暫停原因（可選）：');
        if (reason === null) return; // 用戶取消

        try {
            const response = await this.makeAdminRequest('suspend_user', { user_id: userId, reason: reason });
            if (response.success) {
                this.showMessage('用戶已暫停', 'success');
                this.loadAllUsers();
            } else {
                this.showMessage('暫停失敗：' + response.message, 'error');
            }
        } catch (error) {
            this.showMessage('暫停失敗：網路錯誤', 'error');
        }
    }

    /**
     * 恢復用戶
     */
    async restoreUser(userId) {
        if (!confirm('確定要恢復這個用戶嗎？')) return;

        try {
            const response = await this.makeAdminRequest('restore_user', { user_id: userId });
            if (response.success) {
                this.showMessage('用戶已恢復', 'success');
                this.loadAllUsers();
            } else {
                this.showMessage('恢復失敗：' + response.message, 'error');
            }
        } catch (error) {
            this.showMessage('恢復失敗：網路錯誤', 'error');
        }
    }

    /**
     * 載入系統設置
     */
    async loadSystemSettings() {
        const container = document.getElementById('systemSettingsContainer');
        if (!container) return;

        try {
            const response = await this.makeAdminRequest('get_system_settings');
            if (response.success) {
                this.renderSystemSettings(response.data.settings);
            } else {
                container.innerHTML = '<div class="alert alert-danger">載入失敗：' + response.message + '</div>';
            }
        } catch (error) {
            container.innerHTML = '<div class="alert alert-danger">載入失敗：網路錯誤</div>';
        }
    }

    /**
     * 渲染系統設置
     */
    renderSystemSettings(settings) {
        const container = document.getElementById('systemSettingsContainer');
        if (!container) return;

        let html = '<form id="systemSettingsForm">';

        // 註冊開關
        const registrationEnabled = settings.registration_enabled ? settings.registration_enabled.value : true;
        html += `
            <div class="mb-3">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="registrationEnabled" ${registrationEnabled ? 'checked' : ''}>
                    <label class="form-check-label" for="registrationEnabled">
                        <strong>允許新用戶註冊</strong>
                    </label>
                </div>
                <small class="form-text text-muted">關閉後，註冊按鈕將被隱藏，新用戶無法註冊</small>
            </div>
        `;

        // 管理員審核要求
        const requireApproval = settings.require_admin_approval ? settings.require_admin_approval.value : true;
        html += `
            <div class="mb-3">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="requireAdminApproval" ${requireApproval ? 'checked' : ''}>
                    <label class="form-check-label" for="requireAdminApproval">
                        <strong>新用戶需要管理員審核</strong>
                    </label>
                </div>
                <small class="form-text text-muted">開啟後，新註冊的用戶需要管理員批准才能登入</small>
            </div>
        `;

        html += `
            <div class="mt-4">
                <button type="button" class="btn btn-primary" onclick="adminManager.saveSystemSettings()">
                    <span class="icon-symbol">💾</span> 保存設置
                </button>
            </div>
        `;

        html += '</form>';
        container.innerHTML = html;
    }

    /**
     * 保存系統設置
     */
    async saveSystemSettings() {
        const registrationEnabled = document.getElementById('registrationEnabled').checked;
        const requireAdminApproval = document.getElementById('requireAdminApproval').checked;

        const settings = {
            registration_enabled: registrationEnabled,
            require_admin_approval: requireAdminApproval
        };

        try {
            const response = await this.makeAdminRequest('update_system_settings', { settings: settings });
            if (response.success) {
                this.showMessage('系統設置已保存', 'success');
                // 更新註冊按鈕的顯示狀態
                this.updateRegistrationButtonVisibility(registrationEnabled);
            } else {
                this.showMessage('保存失敗：' + response.message, 'error');
            }
        } catch (error) {
            this.showMessage('保存失敗：網路錯誤', 'error');
        }
    }

    /**
     * 更新註冊按鈕的顯示狀態
     */
    updateRegistrationButtonVisibility(enabled) {
        const registerBtn = document.getElementById('registerBtn');
        if (registerBtn) {
            registerBtn.style.display = enabled ? 'inline-block' : 'none';
        }
    }

    /**
     * 發送管理員API請求
     */
    async makeAdminRequest(endpoint, data = null) {
        const url = endpoint.includes('?') ? `php/admin.php?${endpoint.split('?')[1]}` : 'php/admin.php';
        const options = {
            method: data ? 'POST' : 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        };

        if (data) {
            if (endpoint.includes('?')) {
                data.action = endpoint.split('?')[0];
            } else {
                data.action = endpoint;
            }
            options.body = JSON.stringify(data);
        } else {
            const action = endpoint.includes('?') ? endpoint.split('?')[0] : endpoint;
            options.body = JSON.stringify({ action: action });
            options.method = 'POST';
        }

        const response = await fetch(url, options);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }

    /**
     * 顯示訊息
     */
    showMessage(message, type = 'info') {
        // 可以使用現有的訊息顯示系統
        if (window.authManager && typeof window.authManager.showMessage === 'function') {
            window.authManager.showMessage(message, type);
        } else {
            alert(message);
        }
    }

    /**
     * HTML轉義
     */
    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text ? text.replace(/[&<>"']/g, m => map[m]) : '';
    }
}

// 全局函數
function showAdminPanel() {
    if (window.adminManager) {
        window.adminManager.showAdminPanel();
    }
}

function searchUsers() {
    if (window.adminManager) {
        window.adminManager.loadAllUsers(1);
    }
}

// 初始化管理員管理器
document.addEventListener('DOMContentLoaded', () => {
    window.adminManager = new AdminManager();
});
