/**
 * ===================================================================
 * Receipt Details Modal Styles
 * 與 Edit Item Modal 保持一致的樣式和配色
 * ===================================================================
 */

/* =================================== */
/* ===== 顏色變數定義             ===== */
/* =================================== */

:root {
  --color-1: rgb(0, 255, 234);
  --color-2: rgb(0, 136, 255);
  --color-3: rgb(255, 213, 0);
  --color-4: rgb(255, 0, 0);
  --color-5: rgb(0, 76, 255);
  --border-color-1: rgb(0, 0, 0);
  --border-color-2: rgb(255, 255, 255);
  --border-color-3: rgb(0, 255, 234);
  --border-color-4: rgb(0, 136, 255);
  --border-color-5: rgb(255, 213, 0);
  --border-color-6: rgb(255, 0, 0);
  --text-color-1: rgb(0, 0, 0);
  --text-color-2: rgb(255, 255, 255);
  --text-color-3: rgb(0, 255, 128);
  --text-color-4: rgb(255, 128, 0);
  --text-color-5: rgb(255, 0, 128);
  
  /* 金黃色按鈕顏色 */
  --gold-primary: #FFD700;
  --gold-secondary: #FFA500;
  --gold-accent: #FF8C00;
  --gold-border: #DAA520;
  --gold-text: #8B4513;
  --gold-hover: #FFED4E;
  --gold-hover-border: #B8860B;
}

/* =================================== */
/* ===== Receipt Details Modal    ===== */
/* =================================== */

/* 彈窗寬度增加50% */
#receiptDetailsModal .modal-dialog {
    max-width: calc(1140px * 1.5); /* modal-xl 原本是 1140px，增加50% */
    width: 90vw;
}

/* Modal Content - 與 Edit Item 一致的樣式 */
#receiptDetailsModal .modal-content {
    border: none;
    border-radius: 20px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(0, 255, 234, 0.2);
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    overflow: hidden;
    color: var(--text-color-2);
}

/* Modal Header - 與 Edit Item 一致的樣式 */
#receiptDetailsModal .modal-header {
    background: linear-gradient(135deg, var(--color-1) 0%, var(--color-2) 100%);
    border: none;
    position: relative;
    overflow: hidden;
    padding: 2rem;
}

#receiptDetailsModal .modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 255, 234, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

#receiptDetailsModal .modal-title {
    color: var(--text-color-2);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    gap: 1rem;
}

#receiptDetailsModal .modal-title i {
    font-size: 1.8rem;
    color: var(--text-color-2);
}

#receiptDetailsModal .btn-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 12px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color-2);
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
}

#receiptDetailsModal .btn-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Modal Body - 與 Edit Item 一致的樣式 */
#receiptDetailsModal .modal-body {
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    padding: 2rem;
}

/* Card 樣式 - 與 Edit Item 的 form-section 一致 */
#receiptDetailsModal .card {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(145deg, #0f3460 0%, #0e4b99 100%);
    border-radius: 16px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 234, 0.2);
}

#receiptDetailsModal .card-header {
    background: transparent;
    border: none;
    padding: 0 0 1rem 0;
    margin-bottom: 1rem;
    border-bottom: 2px solid rgba(0, 255, 234, 0.3);
}

#receiptDetailsModal .card-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--color-1);
}

#receiptDetailsModal .card-header i {
    font-size: 1.3rem;
    color: var(--color-1);
}

#receiptDetailsModal .card-body {
    background: transparent;
    padding: 0;
    color: var(--text-color-2);
}

/* Enhanced Table Styles */
#receiptDetailsModal .table-responsive {
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 1rem;
}

#receiptDetailsModal .receipt-details-table {
    color: var(--text-color-2);
    margin-bottom: 0;
    background: #d44444;
    width: 100%;
    min-width: 900px;
}

#receiptDetailsModal .receipt-details-table thead th {
    background: rgba(0, 255, 234, 0.1);
    border-color: rgba(0, 255, 234, 0.3);
    color: var(--color-1);
    font-weight: 700;
    padding: 1.2rem 0.8rem;
    text-align: center;
    font-size: 0.95rem;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

#receiptDetailsModal .receipt-details-table tbody td {
    border-color: rgba(0, 255, 234, 0.2);
    color: var(--text-color-2);
    padding: 1rem 0.8rem;
    vertical-align: middle;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#receiptDetailsModal .receipt-details-table tbody tr:hover {
    background-color: rgba(0, 255, 234, 0.1);
    transition: background-color 0.2s ease;
}

#receiptDetailsModal .receipt-details-table .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
}

#receiptDetailsModal .table-striped tbody tr:nth-of-type(odd) {
    background: rgba(0, 255, 234, 0.05);
}

/* Badge 樣式 */
#receiptDetailsModal .badge {
    background: var(--color-2) !important;
    color: var(--text-color-2);
    border: 1px solid rgba(0, 136, 255, 0.3);
}

#receiptDetailsModal .badge.bg-primary {
    background: var(--color-1) !important;
    color: var(--text-color-1);
}

#receiptDetailsModal .badge.bg-secondary {
    background: var(--color-2) !important;
    color: var(--text-color-2);
}

/* 文字顏色 */
#receiptDetailsModal .text-primary {
    color: var(--color-1) !important;
}

#receiptDetailsModal .text-success {
    color: var(--text-color-3) !important;
}

#receiptDetailsModal .text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* HR 分隔線 */
#receiptDetailsModal hr {
    border-color: rgba(0, 255, 234, 0.2);
    margin: 0.75rem 0;
}

/* Modal Footer - 與 Edit Item 一致的樣式 */
#receiptDetailsModal .modal-footer {
    background: linear-gradient(145deg, #0f3460 0%, #0e4b99 100%);
    border: none;
    border-top: 1px solid rgba(0, 255, 234, 0.2);
    gap: 1rem;
    justify-content: flex-end;
    padding: 1.5rem 2rem;
}

/* =================================== */
/* ===== 金黃色按鈕樣式             ===== */
/* =================================== */

/* Edit Receipt 按鈕 - 金黃色主要按鈕 */
#receiptDetailsModal .modal-footer .btn:first-child {
    background: linear-gradient(135deg, var(--gold-primary) 0%, var(--gold-secondary) 50%, var(--gold-accent) 100%);
    border: 2px solid var(--gold-border);
    color: var(--gold-text);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

#receiptDetailsModal .modal-footer .btn:first-child:hover {
    background: linear-gradient(135deg, var(--gold-hover) 0%, var(--gold-primary) 50%, var(--gold-secondary) 100%);
    border-color: var(--gold-hover-border);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* Print Receipt 按鈕 - 金黃色次要按鈕 */
#receiptDetailsModal .modal-footer .btn:nth-child(2) {
    background: linear-gradient(135deg, var(--gold-secondary) 0%, var(--gold-accent) 100%);
    border: 2px solid var(--gold-border);
    color: var(--gold-text);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 165, 0, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

#receiptDetailsModal .modal-footer .btn:nth-child(2):hover {
    background: linear-gradient(135deg, var(--gold-primary) 0%, var(--gold-secondary) 100%);
    border-color: var(--gold-hover-border);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

/* Close 按鈕 - 金黃色輔助按鈕 */
#receiptDetailsModal .modal-footer .btn:last-child {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 165, 0, 0.2) 100%);
    border: 2px solid rgba(218, 165, 32, 0.5);
    color: var(--text-color-2);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

#receiptDetailsModal .modal-footer .btn:last-child:hover {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(255, 165, 0, 0.3) 100%);
    border-color: var(--gold-border);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.2);
}

/* 按鈕圖標樣式 */
#receiptDetailsModal .modal-footer .btn i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

/* =================================== */
/* ===== 響應式設計                ===== */
/* =================================== */

@media (max-width: 1400px) {
    #receiptDetailsModal .modal-dialog {
        max-width: 95vw;
        width: 95vw;
    }
}

@media (max-width: 768px) {
    #receiptDetailsModal .modal-dialog {
        max-width: 98vw;
        width: 98vw;
        margin: 0.5rem;
    }
    
    #receiptDetailsModal .modal-header,
    #receiptDetailsModal .modal-body,
    #receiptDetailsModal .modal-footer {
        padding: 1rem;
    }
    
    #receiptDetailsModal .modal-title {
        font-size: 1.2rem;
    }
    
    #receiptDetailsModal .modal-footer {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    #receiptDetailsModal .modal-footer .btn {
        width: 100%;
        margin: 0;
    }
    
    #receiptDetailsModal .card {
        padding: 1rem;
        margin-bottom: 1rem;
    }
}

/* =================================== */
/* ===== 動畫效果                  ===== */
/* =================================== */

#receiptDetailsModal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px);
}

#receiptDetailsModal.show .modal-dialog {
    transform: none;
}

/* 卡片進入動畫 */
#receiptDetailsModal .card {
    animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 按鈕懸停動畫 */
#receiptDetailsModal .modal-footer .btn {
    position: relative;
    overflow: hidden;
}

#receiptDetailsModal .modal-footer .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#receiptDetailsModal .modal-footer .btn:hover::before {
    left: 100%;
}