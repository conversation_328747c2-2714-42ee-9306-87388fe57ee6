<?php
session_start();
require_once 'php/config.php';
require_once 'php/auth.php';
require_once 'php/UserManager.php';

// 檢查用戶是否已登入
$isLoggedIn = isset($_SESSION['user_id']);
$currentUser = null;
$isAdmin = false;

if ($isLoggedIn) {
    $userManager = new UserManager();
    $currentUser = $userManager->getUserById($_SESSION['user_id']);
    $isAdmin = $currentUser && $currentUser['role'] === 'admin';
}
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-lang="app_title">KMS PC 收據製作器</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 自定義 CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/receipt.css">
    <link rel="stylesheet" href="css/custom-modals.css">
    <link rel="stylesheet" href="css/auth-styles.css">
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <span class="icon-symbol">🧾</span>
                <span data-lang="app_title">KMS PC 收據製作器</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php if ($isLoggedIn): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showReceiptHistory()">
                            <span class="icon-symbol">📋</span>
                            <span data-lang="receipt_history">收據歷史</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#configModal">
                            <span class="icon-symbol">⚙️</span>
                            <span data-lang="settings">設定</span>
                        </a>
                    </li>
                    <?php if ($isAdmin): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#adminPanelModal">
                            <span class="icon-symbol">👑</span>
                            <span data-lang="admin_panel">管理員面板</span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown">
                            <span class="icon-symbol">🌐</span>
                            <span data-lang="language">語言</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('zh-TW')">繁體中文</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('zh-CN')">简体中文</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('en')">English</a></li>
                        </ul>
                    </li>
                    
                    <?php if ($isLoggedIn): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <span class="icon-symbol">👤</span>
                            <?php echo htmlspecialchars($currentUser['username']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#userSettingsModal">
                                <span class="icon-symbol">⚙️</span>
                                <span data-lang="user_settings">用戶設定</span>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <span class="icon-symbol">🚪</span>
                                <span data-lang="logout">登出</span>
                            </a></li>
                        </ul>
                    </li>
                    <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#loginModal">
                            <span class="icon-symbol">🔑</span>
                            <span data-lang="login">登入</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#registerModal">
                            <span class="icon-symbol">👤</span>
                            <span data-lang="register">註冊</span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要內容 -->
    <div class="container-fluid mt-4">
        <?php if ($isLoggedIn): ?>
        <div class="row">
            <!-- 左側面板 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <span class="icon-symbol">🛠️</span>
                            <span data-lang="receipt_tools">收據工具</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 收據生成區 -->
                        <div class="mb-3">
                            <button class="btn btn-golden-primary w-100 mb-2" data-bs-toggle="modal" data-bs-target="#addItemModal">
                                <span class="icon-symbol">➕</span>
                                <span data-lang="add_item">新增項目</span>
                            </button>
                            <button class="btn btn-golden w-100 mb-2" onclick="clearReceipt()">
                                <span class="icon-symbol">🗑️</span>
                                <span data-lang="clear_receipt">清空收據</span>
                            </button>
                        </div>
                        
                        <!-- 預設項目選擇 -->
                        <div class="mb-3">
                            <h6 data-lang="preset_items">預設項目</h6>
                            <div class="preset-items-container">
                                <div class="preset-item" onclick="addPresetItem('computer_repair', '電腦維修', 1500)">
                                    <span class="icon-symbol">🔧</span>
                                    <span data-lang="computer_repair">電腦維修</span>
                                </div>
                                <div class="preset-item" onclick="addPresetItem('software_install', '軟體安裝', 500)">
                                    <span class="icon-symbol">💿</span>
                                    <span data-lang="software_install">軟體安裝</span>
                                </div>
                                <div class="preset-item" onclick="addPresetItem('data_recovery', '資料救援', 2000)">
                                    <span class="icon-symbol">💾</span>
                                    <span data-lang="data_recovery">資料救援</span>
                                </div>
                                <div class="preset-item" onclick="addPresetItem('system_optimization', '系統優化', 800)">
                                    <span class="icon-symbol">⚡</span>
                                    <span data-lang="system_optimization">系統優化</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 收據操作 -->
                        <div class="mb-3">
                            <h6 data-lang="receipt_actions">收據操作</h6>
                            <button class="btn btn-success w-100 mb-2" onclick="saveReceipt()">
                                <span class="icon-symbol">💾</span>
                                <span data-lang="save_receipt">保存收據</span>
                            </button>
                            <button class="btn btn-info w-100 mb-2" onclick="previewReceipt()">
                                <span class="icon-symbol">👁️</span>
                                <span data-lang="preview_receipt">預覽收據</span>
                            </button>
                            <button class="btn btn-primary w-100" onclick="printReceipt()">
                                <span class="icon-symbol">🖨️</span>
                                <span data-lang="print_receipt">列印收據</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右側收據預覽 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <span class="icon-symbol">📄</span>
                            <span data-lang="receipt_preview">收據預覽</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="receiptPreview" class="receipt-container">
                            <!-- 收據內容將在這裡動態生成 -->
                            <div class="receipt-header">
                                <h3 class="company-name" data-lang="company_name">KMS PC 維修中心</h3>
                                <p class="company-info">
                                    <span data-lang="company_address">台北市信義區信義路五段7號</span><br>
                                    <span data-lang="company_phone">電話：02-2345-6789</span>
                                </p>
                                <hr>
                            </div>
                            
                            <div class="receipt-body">
                                <div class="receipt-info">
                                    <div class="row">
                                        <div class="col-6">
                                            <strong data-lang="receipt_number">收據編號：</strong>
                                            <span id="receiptNumber">R<?php echo date('Ymd') . sprintf('%04d', rand(1, 9999)); ?></span>
                                        </div>
                                        <div class="col-6 text-end">
                                            <strong data-lang="receipt_date">日期：</strong>
                                            <span id="receiptDate"><?php echo date('Y-m-d H:i:s'); ?></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <table class="table table-bordered receipt-table" id="receiptTable">
                                    <thead>
                                        <tr>
                                            <th data-lang="item_name">項目名稱</th>
                                            <th data-lang="quantity">數量</th>
                                            <th data-lang="unit_price">單價</th>
                                            <th data-lang="total_price">總價</th>
                                            <th data-lang="actions">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="receiptItems">
                                        <!-- 收據項目將在這裡動態添加 -->
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <td colspan="3" class="text-end"><strong data-lang="total_amount">總金額：</strong></td>
                                            <td><strong id="totalAmount">$0.00</strong></td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            
                            <div class="receipt-footer">
                                <p class="text-center">
                                    <small data-lang="receipt_footer">感謝您的惠顧，歡迎再次光臨！</small>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <!-- 未登入用戶的歡迎頁面 -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card text-center">
                    <div class="card-body">
                        <h1 class="card-title">
                            <span class="icon-symbol">🧾</span>
                            <span data-lang="welcome_title">歡迎使用 KMS PC 收據製作器</span>
                        </h1>
                        <p class="card-text lead" data-lang="welcome_description">
                            專業的收據製作工具，讓您輕鬆管理和生成收據。
                        </p>
                        <div class="mt-4">
                            <button class="btn btn-primary btn-lg me-3" data-bs-toggle="modal" data-bs-target="#loginModal">
                                <span class="icon-symbol">🔑</span>
                                <span data-lang="login">登入</span>
                            </button>
                            <button class="btn btn-outline-primary btn-lg" data-bs-toggle="modal" data-bs-target="#registerModal">
                                <span class="icon-symbol">👤</span>
                                <span data-lang="register">註冊</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- 包含模態框 -->
    <?php include 'auth-modals.php'; ?>
    <?php include 'other-modals.php'; ?>
    <?php if ($isAdmin): ?>
    <?php include 'admin-panel.php'; ?>
    <?php endif; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定義 JavaScript -->
    <script src="js/language.js"></script>
    <script src="js/receipt.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        // 初始化應用
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            <?php if ($isLoggedIn): ?>
            loadUserData();
            <?php endif; ?>
        });
    </script>
</body>
</html>