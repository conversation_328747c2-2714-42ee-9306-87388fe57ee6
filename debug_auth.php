<?php
// Debug auth.php input parsing
echo "Debug auth.php input parsing..." . PHP_EOL;

// Simulate the same conditions as auth.php
$input = file_get_contents('php://input');
echo "Raw input: " . var_export($input, true) . PHP_EOL;

$decoded = json_decode($input, true);
echo "Decoded input: " . var_export($decoded, true) . PHP_EOL;

$action = $decoded['action'] ?? $_GET['action'] ?? '';
echo "Action: " . var_export($action, true) . PHP_EOL;

$method = $_SERVER['REQUEST_METHOD'];
echo "Method: " . var_export($method, true) . PHP_EOL;

// Check if we have POST data
echo "POST data: " . var_export($_POST, true) . PHP_EOL;
echo "GET data: " . var_export($_GET, true) . PHP_EOL;
?>
