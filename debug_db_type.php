<?php
require_once 'php/config.php';
require_once 'php/UserManager.php';

echo "Debug: Checking database type in UserManager..." . PHP_EOL;

try {
    $userManager = new UserManager();
    
    // Use reflection to access private property
    $reflection = new ReflectionClass($userManager);
    $dbProperty = $reflection->getProperty('db');
    $dbProperty->setAccessible(true);
    $db = $dbProperty->getValue($userManager);
    
    echo "Database type: " . get_class($db) . PHP_EOL;
    
    if ($db instanceof FileDatabase) {
        echo "Using FileDatabase" . PHP_EOL;
        
        // Test FileDatabase directly
        $result = $db->verifyPassword('testuser', 'password123');
        echo "FileDatabase verifyPassword result: " . ($result ? 'SUCCESS' : 'FAILED') . PHP_EOL;
        if ($result) {
            echo "User data: " . json_encode($result, JSON_PRETTY_PRINT) . PHP_EOL;
        }
    } else {
        echo "Using MySQL database" . PHP_EOL;
        
        // Test if we can connect to MySQL
        try {
            $connection = $db->getConnection();
            echo "MySQL connection: SUCCESS" . PHP_EOL;
        } catch (Exception $e) {
            echo "MySQL connection: FAILED - " . $e->getMessage() . PHP_EOL;
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
    echo "Stack trace: " . $e->getTraceAsString() . PHP_EOL;
}
?>
