<?php
/**
 * 刪除收據配置
 * KMS PC Receipt Maker
 */

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('只允許POST請求', 405);
}

try {
    // 獲取配置ID
    $configId = isset($_POST['id']) ? intval($_POST['id']) : 0;
    
    if ($configId <= 0) {
        Response::error('無效的配置ID', 400);
    }
    
    $db = new DatabaseMySQLi();
    
    // 檢查配置是否存在
    $checkSql = "SELECT id FROM receipt_configurations WHERE id = ?";
    $existing = $db->fetch($checkSql, [$configId]);
    
    if (!$existing) {
        Response::error('配置不存在', 404);
    }
    
    // 刪除配置
    $sql = "DELETE FROM receipt_configurations WHERE id = ?";
    $affectedRows = $db->execute($sql, [$configId]);
    
    if ($affectedRows > 0) {
        Response::success(['id' => $configId], '配置刪除成功');
    } else {
        Response::error('刪除配置失敗');
    }
    
} catch (Exception $e) {
    error_log('Delete configuration error: ' . $e->getMessage());
    Response::error('刪除配置失敗: ' . $e->getMessage());
}
?>