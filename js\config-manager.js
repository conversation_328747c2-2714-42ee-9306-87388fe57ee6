/**
 * 配置管理模組
 * KMS PC Receipt Maker
 */

// 配置管理類
class ConfigurationManager {
    constructor() {
        this.savedConfigurations = [];
    }

    /**
     * 保存收據配置
     */
    async saveReceiptConfiguration() {
        const configName = await showPrompt(
            LanguageManager.getText('enter_config_name') || '請輸入配置名稱:',
            '',
            '保存配置'
        );

        if (!configName || configName.trim() === '') {
            return;
        }

        const configuration = {
            name: configName.trim(),
            customerInfo: {
                name: document.getElementById('customerName').value.trim(),
                phone: document.getElementById('customerPhone').value.trim(),
                email: document.getElementById('customerEmail').value.trim(),
                address: document.getElementById('customerAddress').value.trim()
            },
            paymentMethod: 'cash', // Default payment method since no paymentMethod field exists
            items: window.ItemManager ? [...window.ItemManager.receiptItems] : [...(window.receiptItems || [])],
            discountAmount: parseFloat(document.getElementById('discountAmount').value) || 0,
            taxRate: parseFloat(document.getElementById('taxRate').value) || 0,
            notes: document.getElementById('notes').value.trim()
        };

        try {
            const response = await fetch('php/save_configuration.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(configuration)
            });

            // 檢查響應是否為 JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const text = await response.text();
                console.error('Non-JSON response:', text);
                showMessage('服務器返回了無效的響應格式，請檢查服務器配置', 'error');
                return;
            }

            const data = await response.json();

            if (data.success) {
                showMessage(LanguageManager.getText('config_saved_success') || '配置保存成功！', 'success');
            } else {
                showMessage(data.message || '配置保存失敗', 'error');
            }
        } catch (error) {
            console.error('Error saving configuration:', error);
            if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
                showMessage('服務器響應格式錯誤，請檢查數據庫連接和服務器配置', 'error');
            } else {
                showMessage('保存配置時發生錯誤: ' + error.message, 'error');
            }
        }
    }

    /**
     * 載入收據配置
     */
    async loadReceiptConfiguration(configId) {
        try {
            const response = await fetch(`php/get_configurations.php`);
            const data = await response.json();

            if (!data.success) {
                showMessage(data.message || '載入配置失敗', 'error');
                return;
            }

            const config = data.data.find(c => c.id == configId);
            
            if (!config) {
                showMessage(LanguageManager.getText('config_not_found') || '找不到指定的配置', 'error');
                return;
            }

            // 載入客戶信息
            document.getElementById('customerName').value = config.customerInfo.name || '';
            document.getElementById('customerPhone').value = config.customerInfo.phone || '';
            document.getElementById('customerEmail').value = config.customerInfo.email || '';
            document.getElementById('customerAddress').value = config.customerInfo.address || '';
            
            // 載入付款方式 (paymentMethod element doesn't exist, so skip this)
            // document.getElementById('paymentMethod').value = config.payment_method || 'cash';
            
            // 載入項目 - ensure proper number types
            if (window.ItemManager) {
                window.ItemManager.receiptItems = config.items.map(item => ({
                    ...item,
                    quantity: parseInt(item.quantity) || 1,
                    unitPrice: parseFloat(item.unitPrice) || 0,
                    totalPrice: parseFloat(item.totalPrice) || 0,
                    originalPrice: parseFloat(item.originalPrice) || 0,
                    specialPrice: parseFloat(item.specialPrice) || 0,
                    discountPercent: parseInt(item.discountPercent) || 0
                }));
                window.ItemManager.updateReceiptItemsDisplay();
                window.ItemManager.updateTotals();
            } else {
                window.receiptItems = [...config.items];
                if (window.updateItemsList) {
                    window.updateItemsList();
                }
            }
            
            // 載入折扣、稅率和備註
            document.getElementById('discountAmount').value = config.discount_amount || 0;
            document.getElementById('taxRate').value = config.tax_rate || 0;
            document.getElementById('notes').value = config.notes || '';
            
            // 重新計算總計
            if (window.calculateTotals) {
                window.calculateTotals();
            }
            
            // 關閉模態框
            const modal = Modal.getInstance(document.getElementById('configurationModal'));
            if (modal) {
                modal.hide();
            }
            
            showMessage(LanguageManager.getText('config_loaded_success') || '配置載入成功！', 'success');
        } catch (error) {
            console.error('Error loading configuration:', error);
            showMessage('載入配置時發生錯誤', 'error');
        }
    }

    /**
     * 顯示配置管理模態框
     */
    async showConfigurationModal() {
        await this.loadConfigurationList();
        const modal = Modal.getInstance(document.getElementById('configurationModal'));
        modal.show();
    }

    /**
     * 獲取已保存的配置
     */
    async getSavedConfigurations() {
        try {
            const response = await fetch('php/get_configurations.php');
            const data = await response.json();
            
            if (data.success) {
                return data.data;
            } else {
                console.error('Error loading configurations:', data.message);
                return [];
            }
        } catch (error) {
            console.error('Error loading configurations:', error);
            return [];
        }
    }

    /**
     * 載入配置列表
     */
    async loadConfigurationList() {
        const configurations = await this.getSavedConfigurations();
        const container = document.getElementById('configurationList');
        
        if (configurations.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">尚未保存任何配置</div>';
            return;
        }
        
        let html = '';
        configurations.forEach(config => {
            const createdDate = new Date(config.created_at).toLocaleString();
            html += `
                <div class="configuration-item border rounded p-3 mb-2">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <strong>${config.name}</strong>
                            <br><small class="text-muted">創建時間: ${createdDate}</small>
                            <br><small class="text-info">項目數量: ${config.items.length}</small>
                            <br><small class="text-secondary">客戶: ${config.customerInfo.name || '未設定'}</small>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-sm btn-primary me-2" onclick="configManager.loadReceiptConfiguration(${config.id})">
                                載入
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="configManager.deleteConfiguration(${config.id})">
                                刪除
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    /**
     * 刪除配置
     */
    async deleteConfiguration(configId) {
        const confirmed = await showConfirm(
            LanguageManager.getText('confirm_delete_config') || '確定要刪除此配置嗎？此操作無法復原。',
            '刪除配置'
        );

        if (!confirmed) {
            return;
        }
        
        try {
            const formData = new FormData();
            formData.append('id', configId);

            const response = await fetch('php/delete_configuration.php', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                await this.loadConfigurationList();
                showMessage(LanguageManager.getText('config_deleted_success') || '配置刪除成功！', 'success');
            } else {
                showMessage(data.message || '配置刪除失敗', 'error');
            }
        } catch (error) {
            console.error('Error deleting configuration:', error);
            showMessage('刪除配置時發生錯誤', 'error');
        }
    }
}

// 創建全局實例
const configManager = new ConfigurationManager();

// 導出到全局作用域
window.configManager = configManager;
window.saveReceiptConfiguration = () => configManager.saveReceiptConfiguration();
window.showConfigurationModal = () => configManager.showConfigurationModal();
window.deleteConfiguration = (id) => configManager.deleteConfiguration(id);
